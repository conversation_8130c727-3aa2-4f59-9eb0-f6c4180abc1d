# 退款服务重构说明

## 概述
对退款服务进行了架构重构，解决了原有架构的事务边界问题和设计复杂性问题。

## 原有问题
1. **事务边界问题**：整个退款流程在一个大事务中，第三方调用超时导致数据库连接长期占用
2. **一致性风险**：第三方成功但本地失败会导致数据不一致
3. **设计复杂性**：过度抽象化，Context→Strategy→Factory→Executor 四层抽象

## 新架构设计
采用四步骤架构，精确控制事务边界：

```
1. 【无事务】退款对应支付单溯源
   - 根据支付类型进行溯源分析
   - 确定实际退款的支付单和退款方式

2. 【有事务】准备数据并落库发保障消息
   - 退款防重检查、创建退款单
   - 发送退款保障MQ消息

3. 【无事务】调用渠道进行退款
   - 调用第三方退款接口
   - 避免第三方超时影响本地事务

4. 【有事务】处理退款结果
   - 更新退款单状态
   - 记录退款流水
```

## 核心改进
- ✅ **事务优化**：大事务(12步骤) → 两个小事务(步骤2+4)
- ✅ **架构简化**：移除过度抽象，直接条件分支处理
- ✅ **风险控制**：第三方调用与本地数据操作解耦

## 使用方式

### 1. 配置文件
在 `application.yml` 中添加配置：

```yaml
hera:
  refund:
    # 启用新架构
    enable-simplified-service: true

    # 灰度比例 (0-100)
    simplified-service-gray-ratio: 10

    # 启用回退机制
    enable-fallback-to-old-service: true
```

### 2. 灰度策略
- **开发环境**：100% 使用新架构
- **测试环境**：100% 使用新架构
- **生产环境**：5% → 20% → 50% → 100% 逐步灰度

### 3. 监控指标
新架构提供详细的监控指标：
- 成功率统计
- 执行时间分析
- 各步骤性能监控
- 回退情况统计

### 4. 紧急回退
如遇问题，可立即回退：
```yaml
hera:
  refund:
    enable-simplified-service: false
```

## 文件结构
```
service/v2/
├── SimplifiedRefundService.java        # 新的主服务类
├── RefundDataPreparer.java             # 数据准备器(步骤2)
├── RefundChannelCaller.java            # 渠道调用器(步骤3)
├── RefundResultProcessor.java          # 结果处理器(步骤4)
├── UnifiedRefundService.java           # 统一入口(已修改)
├── config/
│   └── RefundServiceConfig.java        # 配置类
└── monitor/
    └── RefundServiceMonitor.java       # 监控组件
```

## 业务逻辑保持不变
- ✅ 支持三种退款场景：直接退款、押金抵扣、预授权退款
- ✅ 保持所有原有的业务校验逻辑
- ✅ 保持所有渠道特定处理
- ✅ 保持汇率转换等特殊逻辑

## 注意事项
1. **向后兼容**：原有接口和调用方式完全不变
2. **安全切换**：支持灰度发布和紧急回退
3. **监控完备**：提供详细的执行指标和性能数据
4. **事务安全**：新架构通过保障消息确保最终一致性

## 部署建议
1. 先在开发环境验证新架构
2. 生产环境小比例灰度(5%)
3. 观察监控指标，逐步提升灰度
4. 确认稳定后全量切换
5. 稳定运行一段时间后可移除旧代码