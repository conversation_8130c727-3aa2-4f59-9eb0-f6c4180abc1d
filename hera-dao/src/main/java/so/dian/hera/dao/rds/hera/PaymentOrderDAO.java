package so.dian.hera.dao.rds.hera;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.chargebolt.hera.domain.PaymentOrderDO;

public interface PaymentOrderDAO {

    /**
     * 动态插入支付记录
     *
     * @param paymentOrderDO 支付信息
     * @return 插入影响行数
     */
    int insertSelective(PaymentOrderDO paymentOrderDO);

    /**
     * 根据业务交易流水号查询支付记录
     *
     * @param bizTradeNo
     * @return
     */
    PaymentOrderDO getPaymentByBizTradeNo(@Param("bizTradeNo") String bizTradeNo);

    /**
     * 根据支付交易流水号查询支付记录
     *
     * @param payTradeNo
     * @return
     */
    PaymentOrderDO getPaymentByPayTradeNo(@Param("payTradeNo") String payTradeNo);

    /**
     * 根据支付交易流水号查询支付记录
     *
     * @param payChannelNo
     * @return
     */
    PaymentOrderDO getPaymentByPayChannelNo(@Param("payChannelNo") String payChannelNo);

    /**
     * 根据支付授权单号查询支付记录
     *
     * @param payAuthNo
     * @return
     */
    List<PaymentOrderDO> getPaymentByPayAuthNo(@Param("payAuthNo") String payAuthNo);

    /**
     * 根据支付流水号在期望状态下更新渠道订单号（幂等保护）
     */
    int updatePayChannelNoIfStatusByPayTradeNo(@Param("payTradeNo") String payTradeNo,
            @Param("oriStatus") Integer oriStatus,
            @Param("payChannelNo") String payChannelNo,
            @Param("updateTime") Date updateTime,
            @Param("gmtUpdate") long gmtUpdate);

    /**
     * 根据支付流水号更新扩展信息
     *
     * @param payTradeNo
     * @param jsonExtensions
     * @return
     */
    int updateJsonExtensionsByPayTradeNo(@Param("payTradeNo") String payTradeNo,
            @Param("oriStatus") Integer oriStatus,
            @Param("status") Integer status,
            @Param("jsonExtensions") String jsonExtensions,
            @Param("updateTime") Date updateTime,
            @Param("gmtUpdate") long gmtUpdate);

    /**
     * 根据支付流水号更新扩展信息
     *
     * @param payTradeNo
     * @param jsonExtensions
     * @return
     */
    int updateExtensionsByPayTradeNo(
            @Param("payTradeNo") String payTradeNo,
            @Param("jsonExtensions") String jsonExtensions,
            @Param("updateTime") Date updateTime,
            @Param("gmtUpdate") long gmtUpdate);

    /**
     * 根据支付流水号更新订单状态
     *
     * @param payTradeNo
     * @param oriStatus
     * @param status
     * @return
     */
    int updateStatusByPayTradeNo(@Param("payTradeNo") String payTradeNo,
            @Param("oriStatus") Integer oriStatus,
            @Param("status") Integer status,
            @Param("updateTime") Date updateTime,
            @Param("gmtUpdate") long gmtUpdate);

    /**
     * 根据支付渠道流水号更新订单状态
     *
     * @param payChannelNo
     * @param oriStatus
     * @param status
     * @return
     */
    int updateStatusByPayChannelNo(@Param("payChannelNo") String payChannelNo,
            @Param("oriStatus") Integer oriStatus,
            @Param("status") Integer status,
            @Param("updateTime") Date updateTime,
            @Param("gmtUpdate") long gmtUpdate);

    /**
     * 根据支付流水号更新订单状态
     *
     * @param payTradeNo
     * @param oriStatus
     * @param status
     * @return
     */
    int updatePaymentStatusByPayTradeNo(@Param("payTradeNo") String payTradeNo,
            @Param("oriStatus") Integer oriStatus,
            @Param("status") Integer status,
            @Param("payChannelNo") String payChannelNo,
            @Param("jsonExtensions") String jsonExtensions,
            @Param("updateTime") Date updateTime,
            @Param("gmtUpdate") long gmtUpdate);

    /**
     * 根据支付流水号更新订单状态
     *
     * @param payTradeNo 支付订单号
     * @param oriStatus  订单原状态
     * @param status     订单目标状态
     * @param gmtUpdate  修改时间戳
     * @param updateTime 修改时间
     * @return
     */
    int updatePaymentOrderStatusByPayTradeNo(@Param("payTradeNo") String payTradeNo,
            @Param("oriStatus") Integer oriStatus,
            @Param("status") Integer status,
            @Param("gmtUpdate") long gmtUpdate,
            @Param("updateTime") Date updateTime);

    /**
     * 根据支付流水号在期望状态下更新支付状态（动态字段：渠道单号/扩展/支付时间），避免 null 覆盖
     */
    int updatePaymentStatusSelectiveByPayTradeNo(@Param("payTradeNo") String payTradeNo,
            @Param("oriStatus") Integer oriStatus,
            @Param("status") Integer status,
            @Param("payChannelNo") String payChannelNo,
            @Param("jsonExtensions") String jsonExtensions,
            @Param("payTime") Date payTime,
            @Param("updateTime") Date updateTime,
            @Param("gmtUpdate") long gmtUpdate);

    void updateRefundStatus(@Param("status") Integer status,
            @Param("refundTime") Date refundTime,
            @Param("refundAmount") Long refundAmount,
            @Param("payTradeNo") String payTradeNo,
            @Param("updateTime") Date updateTime,
            @Param("gmtUpdate") long gmtUpdate);

    /**
     * 根据请求条件分页查询支付单总数
     *
     * @param statusList 支付单状态列表
     * @param bizType    业务类型
     * @param userId     用户ID
     * @param payTradeNo 支付单交易号
     * @param bizTradeNo 交易单交易号
     * @return
     */
    int selectByPageTotal(@Param("statusList") Set<Integer> statusList,
            @Param("bizType") Integer bizType,
            @Param("userId") Long userId,
            @Param("payTradeNo") String payTradeNo,
            @Param("bizTradeNo") String bizTradeNo);

    /**
     * 根据请求条件分页查询支付单
     *
     * @param statusList 支付单状态列表
     * @param bizType    业务类型
     * @param userId     用户ID
     * @param payTradeNo 支付单交易号
     * @param bizTradeNo 交易单交易号
     * @param offset     分页偏移
     * @param pageSize   分页大小
     * @return
     */
    List<PaymentOrderDO> selectByPage(@Param("statusList") Set<Integer> statusList,
            @Param("bizType") Integer bizType,
            @Param("userId") Long userId,
            @Param("payTradeNo") String payTradeNo,
            @Param("bizTradeNo") String bizTradeNo,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize);
}
