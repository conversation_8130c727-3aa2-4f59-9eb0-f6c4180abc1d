<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.rds.hera.PaymentOrderDAO">

    <!-- 动态插入 -->
    <insert id="insertSelective" keyProperty="id" parameterType="com.chargebolt.hera.domain.PaymentOrderDO">
        INSERT INTO cb_hera.payment_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="bizTradeNo != null">biz_trade_no,</if>
            <if test="payTradeNo != null">pay_trade_no,</if>
            <if test="payChannelNo != null">pay_channel_no,</if>
            <if test="payAuthNo != null">pay_auth_no,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payMethod != null">pay_method,</if>
            <if test="clientType != null">client_type,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="payConfigId != null">pay_config_id,</if>
            <if test="status != null">status,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="channelDiscountAmount != null">channel_discount_amount,</if>
            <if test="currency != null">currency,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="refundTime != null">refund_time,</if>
            <if test="jsonExtensions != null">json_extensions,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtUpdate != null">gmt_update,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="bizTradeNo != null">#{bizTradeNo},</if>
            <if test="payTradeNo != null">#{payTradeNo},</if>
            <if test="payChannelNo != null">#{payChannelNo},</if>
            <if test="payAuthNo != null">#{payAuthNo},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payMethod != null">#{payMethod},</if>
            <if test="clientType != null">#{clientType},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="payConfigId != null">#{payConfigId},</if>
            <if test="status != null">#{status},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="channelDiscountAmount != null">#{channelDiscountAmount},</if>
            <if test="currency != null">#{currency},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="refundTime != null">#{refundTime},</if>
            <if test="jsonExtensions != null">#{jsonExtensions},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtUpdate != null">#{gmtUpdate},</if>
        </trim>
    </insert>

    <sql id="Base_Column_List"> id, user_id, biz_trade_no, pay_trade_no, pay_channel_no,
        pay_auth_no, biz_type, pay_type, pay_method, client_type, tenant_id, pay_config_id, status,
        pay_amount, refund_amount, channel_discount_amount, currency, pay_time, refund_time,
        json_extensions, create_time, update_time, gmt_create, gmt_update </sql>

    <!-- 根据业务交易流水号查询支付记录 -->
    <!-- getPaymentByBizTradeNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 biz_trade_no 索引 
    --> 
    <select id="getPaymentByBizTradeNo" resultType="com.chargebolt.hera.domain.PaymentOrderDO">
        select * from cb_hera.payment_order where biz_trade_no = #{bizTradeNo}
    </select>

    <!-- 根据支付交易流水号查询支付记录 -->
    <!-- getPaymentByPayTradeNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_trade_no 索引 
    --> 
    <select id="getPaymentByPayTradeNo" resultType="com.chargebolt.hera.domain.PaymentOrderDO">
        select * from cb_hera.payment_order where pay_trade_no = #{payTradeNo}
    </select>

    <!-- 根据支付渠道流水号查询支付记录 -->
    <!-- getPaymentByPayChannelNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_channel_no 索引 
    --> 
    <select id="getPaymentByPayChannelNo" resultType="com.chargebolt.hera.domain.PaymentOrderDO">
        select * from cb_hera.payment_order where pay_channel_no = #{payChannelNo}
    </select>

    <!-- 根据支付授权单号查询支付记录 -->
    <!-- getPaymentByPayAuthNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_auth_no 索引 
    --> 
    <select id="getPaymentByPayAuthNo" resultType="com.chargebolt.hera.domain.PaymentOrderDO">
        select * from cb_hera.payment_order where pay_auth_no = #{payAuthNo}
    </select>

    <!-- 根据支付流水号更新订单状态 -->
    <!-- updateStatusByPayTradeNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_trade_no 索引 
    --> 
    <update id="updateStatusByPayTradeNo">
        update cb_hera.payment_order
        set status = #{status},
            update_time = #{updateTime},
            gmt_update = #{gmtUpdate}
        where
            pay_trade_no = #{payTradeNo}
        and status = #{oriStatus}
    </update>

    <!-- 根据支付流水号在期望状态下更新渠道订单号（幂等保护） -->
    <!-- updatePayChannelNoIfStatusByPayTradeNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_trade_no 索引 
    --> 
    <update id="updatePayChannelNoIfStatusByPayTradeNo"> 
        update cb_hera.payment_order 
        set pay_channel_no = #{payChannelNo}, 
            update_time = #{updateTime}, 
            gmt_update = #{gmtUpdate} 
        where pay_trade_no = #{payTradeNo} 
        and `status` = #{oriStatus} 
    </update>

    <!-- 根据支付流水号更新扩展信息 -->
    <!-- updateJsonExtensionsByPayTradeNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_trade_no 索引 
    --> 
    <update id="updateJsonExtensionsByPayTradeNo">
        update cb_hera.payment_order
        set `status` = #{status},
            json_extensions = #{jsonExtensions},
            update_time = #{updateTime},
            gmt_update = #{gmtUpdate}
        where
            pay_trade_no = #{payTradeNo}
        and `status` = #{oriStatus}
    </update>

    <!-- 根据支付流水号更新支付状态和渠道订单号 -->
    <!-- updatePaymentStatusByPayTradeNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_trade_no 索引 
    --> 
    <update id="updatePaymentStatusByPayTradeNo">
        update cb_hera.payment_order
        set `status` = #{status},
            pay_channel_no = #{payChannelNo},
            json_extensions = #{jsonExtensions},
            update_time = #{updateTime},
            gmt_update = #{gmtUpdate}
        where
            pay_trade_no = #{payTradeNo}
        and `status` = #{oriStatus}
    </update>

    <!-- updatePaymentOrderStatusByPayTradeNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年
      每日调用量 100+ 次，走 pay_trade_no 索引
    -->
    <update id="updatePaymentOrderStatusByPayTradeNo">
        update cb_hera.payment_order
        set `status` = #{status},
        update_time = #{updateTime},
        gmt_update = #{gmtUpdate}
        where pay_trade_no = #{payTradeNo}
    </update>


    <!-- 根据支付流水号更新支付状态（可选字段动态设置，避免用 null 覆盖），并在成功时写入 pay_time -->
    <!-- updatePaymentStatusSelectiveByPayTradeNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_trade_no 索引 
    --> 
    <update id="updatePaymentStatusSelectiveByPayTradeNo"> 
        update cb_hera.payment_order
        set `status` = #{status}, 
        <if test="payChannelNo != null"> pay_channel_no = #{payChannelNo}, </if>
        <if test="jsonExtensions != null"> json_extensions = #{jsonExtensions}, </if>
        <if test="payTime != null"> pay_time = #{payTime}, </if> 
        update_time = #{updateTime}, 
        gmt_update = #{gmtUpdate} 
        where pay_trade_no = #{payTradeNo} 
        and `status` = #{oriStatus} 
    </update>


    <!-- updateRefundStatus 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_trade_no 索引 
    --> 
    <update id="updateRefundStatus">
        update cb_hera.payment_order
        set `status` = #{status},
            refund_time = #{refundTime},
            refund_amount = #{refundAmount},
            update_time = #{updateTime},
            gmt_update = #{gmtUpdate}
        where pay_trade_no = #{payTradeNo}
    </update>


    <!-- selectByPageTotal 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，可能不走索引，如果入参为空的话 
    --> 
    <select id="selectByPageTotal" resultType="int">
        SELECT count(id)
        FROM cb_hera.payment_order
        <where>
            <if test="statusList != null">
                AND status IN
                <foreach collection="statusList" item="status" index="index" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="bizType != null">
                AND biz_type = #{bizType}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="payTradeNo != null and payTradeNo != ''">
                AND pay_trade_no = #{payTradeNo}
            </if>
            <if test="bizTradeNo != null and bizTradeNo != ''">
                AND biz_trade_no = #{bizTradeNo}
            </if>
        </where>
    </select>

    <!-- 根据请求条件分页查询支付单 -->
    <!-- selectByPage 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，可能不走索引，如果入参为空的话 
    --> 
    <select id="selectByPage" resultType="com.chargebolt.hera.domain.PaymentOrderDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cb_hera.payment_order
        <where>
            <if test="statusList != null">
                AND status IN
                <foreach collection="statusList" item="status" index="index" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="bizType != null">
                AND biz_type = #{bizType}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="payTradeNo != null and payTradeNo != ''">
                AND pay_trade_no = #{payTradeNo}
            </if>
            <if test="bizTradeNo != null and bizTradeNo != ''">
                AND biz_trade_no = #{bizTradeNo}
            </if>
        </where>
        ORDER BY id DESC
        LIMIT ${offset}, ${pageSize}
    </select>

    <!-- updateStatusByPayChannelNo --> 
    <!-- updateStatusByPayChannelNo 数据量 100+ 预计每日增量 40+，百万数据量预计需 50 年 
        每日调用量 100+ 次，走 pay_channel_no 索引 
    --> 
    <update id="updateStatusByPayChannelNo">
            update cb_hera.payment_order
            set `status` = #{status},
                update_time = #{updateTime},
                gmt_update = #{gmtUpdate}
            where
                pay_channel_no = #{payChannelNo}
            and `status` = #{oriStatus}
    </update>

    <update id="updateExtensionsByPayTradeNo">
        update cb_hera.payment_order
        set json_extensions = #{jsonExtensions},
            update_time = #{updateTime},
            gmt_update = #{gmtUpdate}
        where
            pay_trade_no = #{payTradeNo}
    </update>
</mapper>
