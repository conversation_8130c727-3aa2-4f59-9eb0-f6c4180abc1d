package com.chargebolt.hera.client.apiv2;

import com.chargebolt.hera.client.dto.param.rq.account.UserAccountQueryRQ;
import com.chargebolt.hera.client.dto.useraccount.UserAccountDTO;
import com.chargebolt.hera.client.enums.account.AccountTypeEnum;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.mofa3.lang.domain.Result;
import java.util.List;

public interface UserAccountQueryApi {

    /**
     * 根据账户ID查询用户账户（所有状态）
     *
     * @param accountId 账户ID
     * @return
     */
    @GetMapping("/api/account/getByAccountId")
    Result<UserAccountDTO> getUserAccountByAccountId(@RequestParam("accountId") Long accountId);

    /**
     * 根据账户ID查询关联的支付单号
     *
     * @param accountId 账户ID
     * @return
     */
    @GetMapping("/api/account/relatePayTradeNoByAccountId")
    Result<String> relatePayTradeNoByAccountId(@RequestParam("accountId") Long accountId);

    /**
     * 根据用户ID和代理商ID和账户类型查询状态为正常的用户账户(状态：正常)
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param accountType 账户类型：1-钱包账户；2-押金账户；3-积分账户；4-赠送金额账户 {@link AccountTypeEnum}
     * @return
     */
    @GetMapping("/api/account/getByParams")
    Result<UserAccountDTO> getUserAccountByParams(@RequestParam("tenantId") Long tenantId,
                                                  @RequestParam(value = "userId") Long userId,
                                                  @RequestParam("accountType") Integer accountType);

    /**
     * 根据用户ID查询符合条件的用户账户(状态：正常)
     *
     * @param rq
     * @return
     */
    @PostMapping("/api/account/getByUserId")
    Result<List<UserAccountDTO>> getUserAccount(@RequestBody UserAccountQueryRQ rq);

    /**
     * 根据用户ID查询最后一个用户账户(状态：正常)
     *
     * @param userId 用户ID
     * @return
     */
    @GetMapping("/api/account/getLastUserAccountByUserId")
    Result<UserAccountDTO> getLastUserAccountByUserId(@RequestParam("userId") Long userId);

    /**
     * 根据用户ID查询用户账户列表(状态：正常)
     *
     * @param userId 用户ID
     * @return
     */
    @GetMapping("/api/account/getUserAccountListByUserId")
    Result<List<UserAccountDTO>> getUserAccountListByUserId(@RequestParam("userId") Long userId);

    /**
     * 根据用户ID和租户ID查询用户账户列表(状态：正常)
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return
     */
    @GetMapping("/api/account/getUserAccountList")
    Result<List<UserAccountDTO>> getUserAccountList(@RequestParam("userId") Long userId, @RequestParam("tenantId") Long tenantId);

    

}
