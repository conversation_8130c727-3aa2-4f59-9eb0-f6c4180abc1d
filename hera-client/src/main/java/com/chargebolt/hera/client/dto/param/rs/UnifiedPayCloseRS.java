package com.chargebolt.hera.client.dto.param.rs;

import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UnifiedPayCloseRS {

    /** 是否申请成功 必填：是 */
    @ApiModelProperty(value = "是否申请成功",required = true)
    private boolean success;

    /** 申请状态 {@link com.chargebolt.hera.client.enums.status.TransStatusEnum} 必填：是*/
    @ApiModelProperty(value = "申请状态: PROCESSING-处理中,SUCCESS-成功,FAIL-失败",required = true)
    private String status;

    /** 支付交易流水号 必填：是 */
    @ApiModelProperty(value = "支付交易流水号",required = true)
    private String payTradeNo;

    /** 支付渠道流水号 必填：否 */
    @ApiModelProperty(value = "支付支付渠道流水号交易流水号")
    private String payChannelNo;

    /** 渠道返回信息 必填：否 */
    @ApiModelProperty(value = "渠道返回信息")
    private ChannelRetMsg channelRetMsg;

    public UnifiedPayCloseRS(boolean success, String payTradeNo, String payChannelNo, ChannelRetMsg channelRetMsg){
        this.success = success;
        this.status = success ? TransStatusEnum.SUCCESS.getKey() : TransStatusEnum.FAIL.getKey();
        this.payTradeNo = payTradeNo;
        this.payChannelNo = payChannelNo;
        this.channelRetMsg = channelRetMsg;
    }

    public static UnifiedPayCloseRS success(String payTradeNo, String payChannelNo, ChannelRetMsg channelRetMsg){
        return new UnifiedPayCloseRS(true,payTradeNo,payChannelNo,channelRetMsg);
    }

    public static UnifiedPayCloseRS fail(String payTradeNo, ChannelRetMsg channelRetMsg){
        return new UnifiedPayCloseRS(false,payTradeNo,"",channelRetMsg);
    }
}
