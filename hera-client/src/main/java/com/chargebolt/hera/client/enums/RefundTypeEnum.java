package com.chargebolt.hera.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * 退款类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RefundTypeEnum {

    REFUND_DEPOSIT(0, "退押金"),
    REFUND_ORDER(1, "退订单"),

    ;

    private Integer code;
    private String desc;

    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举值，未找到返回null
     */
    public static RefundTypeEnum explain(Integer code) {
        Assert.notNull(code, "code should not be null");
        for (RefundTypeEnum item : RefundTypeEnum.values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     * 
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return explain(code) != null;
    }
}
