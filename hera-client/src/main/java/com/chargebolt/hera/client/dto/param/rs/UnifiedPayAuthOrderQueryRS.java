package com.chargebolt.hera.client.dto.param.rs;

import java.io.Serializable;
import java.util.Date;

import com.chargebolt.hera.client.enums.PayAuthTypeEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayAuthOrderQueryRS implements Serializable {

    /** 授权ID，唯一标识每条记录 */
    @ApiModelProperty(value = "授权ID",required = true)
    private Long id;

    /** 用户ID，关联用户表 */
    @ApiModelProperty(value = "用户ID",required = true)
    private Long userId;

    /** 租户ID */
    @ApiModelProperty(value = "租户ID",required = true)
    private Long tenantId;

    /** 支付授权凭证编号 */
    @ApiModelProperty(value = "支付授权凭证编号",required = true)
    private String payAuthNo;

    /** 支付授权类型（1：押金，2：预授权，3：代扣）{@link PayAuthTypeEnum} */
    @ApiModelProperty(value = "支付授权类型，1：押金，2：预授权，3：代扣",required = true)
    private Integer payAuthType;

    /** 授权金额（押金金额、预授权金额、代扣金额） */
    @ApiModelProperty(value = "授权金额",required = true)
    private Integer amount;

    /** 货币类型（如CNY、USD等） */
    @ApiModelProperty(value = "货币类型",required = true)
    private String currency;

    /** 状态（根据记录类型不同含义不同）{@link com.chargebolt.hera.client.enums.status.RentalAuthStatus} */
    @ApiModelProperty(value = "状态，1：未支付，2：已支付，3：已过期",required = true)
    private Integer status;

    /** 支付渠道（如支付宝、微信、银行卡等）{@link com.chargebolt.hera.client.enums.PaywayEnum} */
    @ApiModelProperty(value = "支付渠道，1：支付宝，2：微信，3：银行卡",required = true)
    private Integer payType;

    /** 支付方式（如余额、支付宝、微信、银行卡等）{@link com.chargebolt.hera.client.enums.PayMethodEnum} */
    @ApiModelProperty(value = "支付方式，1：余额，2：支付宝，3：微信，4：银行卡",required = true)
    private Integer payMethod;

    /** 当前关联订单编号 */
    @ApiModelProperty(value = "当前关联订单编号",required = true)
    private String bizAuthNo;

    /**
     * 预授权时，是三方单号
     * 代扣时，是三方单号
     * 押金充值时，是押金充值支付单号
     * */
    @ApiModelProperty(value = "预授权时，是三方单号，代扣时，是三方单号，押金充值时，是押金充值支付单号",required = true)
    private String channelAuthNo;

    /** 扩展字段，用于存储其他非通用信息 {@link com.chargebolt.hera.domain.PaymentAuthJsonExtensions }*/
    @ApiModelProperty(value = "扩展字段，用于存储其他非通用信息",required = true)
    private String jsonExtensions;

    /** 备注信息 */
    @ApiModelProperty(value = "备注信息")
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 过期时间 */
    @ApiModelProperty(value = "过期时间")
    private Date expireTime;

    /** 创建时间戳 */
    private Long gmtCreate;

    /** 更新时间戳 */
    private Long gmtUpdate;

}
