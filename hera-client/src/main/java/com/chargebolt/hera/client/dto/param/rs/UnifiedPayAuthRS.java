package com.chargebolt.hera.client.dto.param.rs;

import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayAuthRS implements java.io.Serializable{

    /** 是否申请成功 必填：是 */
    private boolean success;

    /** 申请状态 {@link com.chargebolt.hera.client.enums.status.TransStatusEnum} 必填：是*/
    private String status;

    /** 商户授权单号 必填：是 */
    @ApiModelProperty(value = "商户授权单号")
    private String rentalAuthNo;

    /** 渠道授权单号 必填：否 */
    @ApiModelProperty(value = "渠道授权单号")
    private String channelAuthNo;

    /** 渠道返回信息 必填：否 */
    @ApiModelProperty(value = "渠道返回信息")
    private ChannelRetMsg channelRetMsg;

    /** 授权结果 {@link com.chargebolt.hera.client.dto.common.PayBusiConstants} 必填：否 */
    @ApiModelProperty(value = "授权结果")
    private Map<String, Object> prepayResult;

    public UnifiedPayAuthRS(){
    }

    public UnifiedPayAuthRS(boolean success, String rentalAuthNo){
        this.success = success;
        this.rentalAuthNo = rentalAuthNo;
    }

    public UnifiedPayAuthRS(boolean success, String rentalAuthNo, String channelAuthNo, Map<String, Object> prepayResult,
                            ChannelRetMsg channelRetMsg){
        this.success = success;
        this.status = success ? TransStatusEnum.SUCCESS.getKey() : TransStatusEnum.FAIL.getKey();
        this.rentalAuthNo = rentalAuthNo;
        this.channelAuthNo = channelAuthNo;
        this.prepayResult = prepayResult;
        this.channelRetMsg = channelRetMsg;
    }

    public static UnifiedPayAuthRS success(String rentalAuthNo, String channelAuthNo, Map<String, Object> prepayResult, ChannelRetMsg channelRetMsg){
        return new UnifiedPayAuthRS(true,rentalAuthNo,channelAuthNo,prepayResult,channelRetMsg);
    }

    public static UnifiedPayAuthRS fail(String rentalAuthNo, ChannelRetMsg channelRetMsg){
        return new UnifiedPayAuthRS(false,rentalAuthNo,"",null,channelRetMsg);
    }
}
