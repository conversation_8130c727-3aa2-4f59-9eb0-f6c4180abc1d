package com.chargebolt.hera.client.dto.param.rq.antom;

import com.chargebolt.hera.client.dto.param.base.Goods;
import com.chargebolt.hera.client.dto.param.rq.UnifiedPayAuthRQ;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class AntomAuthRQ extends UnifiedPayAuthRQ {

    /** 商品ID 可传充电宝设备编号 */
    @ApiModelProperty(value = "商品ID",required = true)
    private Goods goods;

}
