package com.chargebolt.hera.client.dto.param.rq;

import java.io.Serializable;

import com.chargebolt.hera.client.dto.param.base.AgentInfo;
import com.chargebolt.hera.client.dto.param.base.CustomerInfo;
import com.chargebolt.hera.client.dto.param.base.SystemInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayBase implements Serializable {

    /** 代理商信息  */
    @ApiModelProperty(value = "代理商信息",required = true)
    protected AgentInfo agentInfo;

    /** 用户信息  */
    @ApiModelProperty(value = "用户信息",required = true)
    protected CustomerInfo customerInfo;

    /** 业务交易流水号 */
    @ApiModelProperty(value = "业务交易流水号",required = true)
    protected String bizTradeNo;

    /** 业务类型 {@link com.chargebolt.hera.client.enums.PaymentBizTypeEnum} */
    @ApiModelProperty(value = "业务类型",required = true)
    protected Integer bizType;

    /** 支付金额(转汇后的金额) */
    @ApiModelProperty(value = "支付金额(转汇前的金额)",required = true)
    protected Long payAmount;

    /** 货币类型(转汇后的金额) */
    @ApiModelProperty(value = "货币类型(转汇前的金额)",required = true)
    protected String currency;

    /** 支付渠道 {@link com.chargebolt.hera.client.enums.PaywayEnum} */
    @ApiModelProperty(value = "支付渠道",required = true)
    protected Integer payWay;

    /** 支付方式 {@link com.chargebolt.hera.client.enums.PayMethodEnum} */
    @ApiModelProperty(value = "支付方式",required = true)
    protected Integer payMethod;

    /** 客户端类型：1-普通h5,2-微信小程序,4-安卓客户端,5-iOS客户端 {@link com.chargebolt.eden.enums.ClientTypeEnum} */
    @ApiModelProperty(value = "客户端类型")
    protected Integer clientType;

    /**
     * 特定渠道发起额外参数
     * MidtransRQ
     * AntomPayRQ
     * AntomCapturePayRQ
     * AirwallexCapturePayRQ
     */
    @ApiModelProperty(value = "特定渠道发起额外参数")
    protected String channelExtra;

    /** 备注,透传给三方 */
    @ApiModelProperty(value = "备注,透传给三方",required = true)
    protected String remark;

    /** 系统信息 */
    @ApiModelProperty(value = "系统信息",required = true)
    protected SystemInfo systemInfo;

}
