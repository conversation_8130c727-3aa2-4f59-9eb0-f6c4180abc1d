package com.chargebolt.hera.client.apiv2;

import com.chargebolt.hera.client.dto.param.rq.UnifiedPayAuthOrderQueryRQ;
import com.chargebolt.hera.client.dto.param.rq.UnifiedPayAuthQueryRQ;
import com.chargebolt.hera.client.dto.param.rs.RentalAuthRecordRS;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthOrderQueryRS;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.mofa3.lang.domain.PageContext;
import so.dian.mofa3.lang.domain.Result;

public interface UnifiedPayAuthQueryApi {

    /**
     * 根据用户信息查询符合条件的授权成功且未被占用的授权凭证
     * @param rq
     * @return
     */
    @PostMapping("/api/payAuth/qryByParams")
    Result<UnifiedPayAuthOrderQueryRS> unifiedPayAuthQryByParams(@RequestBody UnifiedPayAuthOrderQueryRQ rq);

    /**
     * 获取用户最新一条预授权记录
     * @param tenantId
     * @param userId
     * @return
     */
    @GetMapping("/api/payAuth/getLastRentalAuthRecord")
    Result<UnifiedPayAuthOrderQueryRS> getLastRentalAuthRecord(@RequestParam("tenantId") Long tenantId, @RequestParam("userId") Long userId);

    /**
     * 统一支付预授权记录查询，无记录返回null
     * @param payAuthNo 支付授权凭证订单号
     * @return
     */
    @PostMapping("/api/payAuth/qryByPayAuthNo")
    Result<UnifiedPayAuthOrderQueryRS> unifiedPayAuthQryByPayAuthNo(@RequestParam("payAuthNo") String payAuthNo);

    /**
     * 按订单号查询授权凭证
     * 如果授权后没有创建订单，rental_order_no为空
     *
     * @param orderNo 订单号
     * @return
     */
    @PostMapping("/api/payAuth/qryByOrderNo")
    Result<UnifiedPayAuthOrderQueryRS> unifiedPayAuthQryByOrderNo(@RequestParam("orderNo") String orderNo);

    /**
     * 按渠道授权单号查询授权凭证
     *
     * @param channelAuthNo 渠道授权单号
     * @return
     */
    @GetMapping("/api/payAuth/qryByChannelAuthNo")
    Result<UnifiedPayAuthOrderQueryRS> unifiedPayAuthQryByChannelAuthNo(@RequestParam("channelAuthNo") String channelAuthNo);

    /**
     * 分页查询授权凭证
     *
     * @param request
     * @return
     */
    @PostMapping("/api/payAuth/qryAuthPage")
    Result<PageContext<RentalAuthRecordRS>> qryAuthPage(@RequestBody UnifiedPayAuthQueryRQ request);
}
