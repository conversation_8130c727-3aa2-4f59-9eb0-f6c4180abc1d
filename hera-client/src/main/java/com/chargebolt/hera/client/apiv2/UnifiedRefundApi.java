package com.chargebolt.hera.client.apiv2;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.client.dto.pay.refund.v2.RefundRQ;

import io.swagger.annotations.Api;
import so.dian.mofa3.lang.domain.Result;

@Api(tags = "退款接口")
public interface UnifiedRefundApi {

    /**
     * 退款申请接口
     * 用于发起退款请求，支持全额退款和部分退款
     * 
     * @param refundRQ
     * @return
     */
    @PostMapping("/api/v2/refund/apply")
    Result<UnifiedRefundRS> refundApply(@RequestBody RefundRQ refundRQ);

    /**
     * 判断是否支持取消
     * 
     * @param payTradeNo 支付单号
     * @return
     */
    @GetMapping("/api/v2/refund/cancel/check")
    Result<Boolean> cancelCheck(@RequestParam(value = "payTradeNo") String payTradeNo);
}
