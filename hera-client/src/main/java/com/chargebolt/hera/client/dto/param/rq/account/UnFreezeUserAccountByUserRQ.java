package com.chargebolt.hera.client.dto.param.rq.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UnFreezeUserAccountByUserRQ implements java.io.Serializable{

    /** 用户ID */
    @ApiModelProperty(value = "用户ID", required = true)
    private Long userId;

    /** 租户ID */
    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    /** 账户类型 {@link com.chargebolt.hera.client.enums.account.AccountTypeEnum} */
    @ApiModelProperty(value = "账户类型 {@link com.chargebolt.hera.client.enums.account.AccountTypeEnum} ", required = true)
    private Integer accountType;

    /** 解冻金额 */
    @ApiModelProperty(value = "解冻金额", required = true)
    private Integer unFreezeAmount;

}
