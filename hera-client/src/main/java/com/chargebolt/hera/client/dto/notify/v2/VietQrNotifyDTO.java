package com.chargebolt.hera.client.dto.notify.v2;

import java.util.Date;

import lombok.Data;

@Data
public class VietQrNotifyDTO {

    /**
     * 交易订单号
     */
    private String tradeNo;
    
    /**
     * 支付方式
     */
    private Integer payway;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 设备号
     */
    private String deviceNo;

    /**
     * 金额（单位：分）
     */
    private Long amount;

    /**
     * 币种
     */
    private String currency;
    /**
     * 渠道扩展通知消息体
     * {@link com.chargebolt.hera.client.dto.notify.VietqrNotifyDTO }
     */
    private String channelExtraNotifyBody;
}
