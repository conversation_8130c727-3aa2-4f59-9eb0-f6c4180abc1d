package com.chargebolt.hera.client.dto.param.rs;

import java.util.Map;

import com.chargebolt.hera.client.dto.param.base.ChannelPayRetMsg;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UnifiedPayRS {
    /** 是否申请成功 必填：是 */
    @ApiModelProperty(value = "是否申请成功", required = true)
    private boolean success;

    /** 申请状态 {@link com.chargebolt.hera.client.enums.status.TransStatusEnum} 必填：是 */
    @ApiModelProperty(value = "申请状态: PROCESSING-处理中,SUCCESS-成功,FAIL-失败", required = true)
    private String status;

    /** 支付交易流水号 必填：是 */
    @ApiModelProperty(value = "支付交易流水号", required = true)
    private String payTradeNo;

    /** 支付渠道流水号 必填：否 */
    @ApiModelProperty(value = "支付支付渠道流水号交易流水号")
    private String payChannelNo;

    /** 预支付结果 {@link com.chargebolt.hera.client.dto.common.PayBusiConstants} 必填：否 */
    @ApiModelProperty(value = "预支付结果")
    private Map<String, Object> prepayResult;

    /** 渠道返回信息 必填：否 */
    @ApiModelProperty(value = "渠道返回信息")
    private ChannelPayRetMsg channelRetMsg;

    public UnifiedPayRS(boolean success, String payTradeNo) {
        this.success = success;
        this.payTradeNo = payTradeNo;
    }

    public UnifiedPayRS(boolean success, String payTradeNo, String payChannelNo, Map<String, Object> prepayResult,
            ChannelPayRetMsg channelRetMsg) {
        this.success = success;
        this.status = success ? TransStatusEnum.SUCCESS.getKey() : TransStatusEnum.FAIL.getKey();
        this.payTradeNo = payTradeNo;
        this.payChannelNo = payChannelNo;
        this.prepayResult = prepayResult;
        this.channelRetMsg = channelRetMsg;
    }

    public static UnifiedPayRS success(String payTradeNo, String payChannelNo, Map<String, Object> prepayResult,
            ChannelPayRetMsg channelRetMsg) {
        return new UnifiedPayRS(true, payTradeNo, payChannelNo, prepayResult, channelRetMsg);
    }

    public static UnifiedPayRS fail(String payTradeNo, ChannelPayRetMsg channelRetMsg) {
        return new UnifiedPayRS(false, payTradeNo, "", null, channelRetMsg);
    }
}
