package com.chargebolt.hera.client.dto.param.rq;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayAuthOrderQueryRQ implements Serializable {

    /** 租户ID */
    @ApiModelProperty(value = "租户ID",required = true)
    private Long tenantId;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID",required = true)
    private Long userId;

    /** 支付授权类型 {@link com.chargebolt.hera.client.enums.PayAuthTypeEnum} */
    @ApiModelProperty(value = "支付授权类型",required = true)
    private Integer payAuthType;

}
