package com.chargebolt.hera.client.apiv2;

import com.chargebolt.hera.client.dto.param.rq.UnifiedPayAuthCancelRQ;
import com.chargebolt.hera.client.dto.param.rq.UnifiedPayAuthRQ;
import com.chargebolt.hera.client.dto.param.rq.UpdateRentalAuthOrderRQ;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import org.springframework.web.bind.annotation.RequestParam;
import so.dian.mofa3.lang.domain.Result;

public interface UnifiedPayAuthApi {

    /**
     * 统一支付预授权
     * @param rq
     * @return
     */
    @PostMapping("/api/pay/unifiedPayAuth")
    Result<UnifiedPayAuthRS> unifiedPayAuth(@RequestBody UnifiedPayAuthRQ rq);

    /**
     * 取消预授权
     */
    @PostMapping("/api/pay/cancelAuth")
    Result<Boolean> cancelAuth(@RequestBody UnifiedPayAuthCancelRQ rq);

    /**
     * 更新租借授权订单
     * @param rq
     * @return
     */
    @PostMapping("/api/pay/updateRentalAuthOrder")
    Result<Boolean> updateRentalAuthOrder(@RequestBody UpdateRentalAuthOrderRQ rq);

}
