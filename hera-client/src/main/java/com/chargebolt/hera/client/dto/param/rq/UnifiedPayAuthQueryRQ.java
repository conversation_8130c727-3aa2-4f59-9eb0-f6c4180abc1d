/*
 * Copyright (c) 2016-2025 Chargebolt
 *
 * This code is proprietary software and may only be accessed by authorized users.
 * Any unauthorized use is considered an infringement.
 *
 */
package com.chargebolt.hera.client.dto.param.rq;

import com.chargebolt.hera.client.dto.param.base.AgentInfo;
import com.chargebolt.hera.client.dto.param.base.CustomerInfo;
import com.chargebolt.hera.client.dto.param.base.PageRequest;
import com.chargebolt.hera.client.enums.PayAuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/*
 * @file       UnifiedPayAuthQueryRQ.java
 * <AUTHOR>
 * @date       2025/9/3 16:25
 * @details    [Detailed notes]
 *
 * MODIFICATION HISTORY:
 * - [Date]     [Modifier]: [Description of Change]
 * -
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UnifiedPayAuthQueryRQ extends PageRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202509246162832L;

    /** 代理商信息  */
    @ApiModelProperty(value = "代理商信息",required = true)
    protected AgentInfo agentInfo;

    /** 用户信息  */
    @ApiModelProperty(value = "用户信息",required = true)
    protected CustomerInfo customerInfo;

    /** 租借授权凭证编号 */
    private String payAuthNo;

    /** 状态（根据记录类型不同含义不同）{@link com.chargebolt.hera.client.enums.status.RentalAuthStatus} */
    private Integer status;

    /** 支付授权类型  {@link PayAuthTypeEnum} */
    @ApiModelProperty(value = "支付授权类型",required = true)
    private Integer payAuthType;
}