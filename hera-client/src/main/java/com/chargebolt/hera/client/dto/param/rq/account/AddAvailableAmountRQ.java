package com.chargebolt.hera.client.dto.param.rq.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddAvailableAmountRQ implements Serializable {

    /** 用户ID */
    @ApiModelProperty(value = "用户ID",required = true)
    private Long userId;

    /** 租户ID */
    @ApiModelProperty(value = "租户ID",required = true)
    private Long tenantId;

    /** 账户类型 {@link com.chargebolt.hera.client.enums.account.AccountTypeEnum} */
    @ApiModelProperty(value = "账户类型",required = true)
    private Integer accountType;

    /** 业务流水号 */
    @ApiModelProperty(value = "业务流水号",required = true)
    private String bizBalanceNo;

    /** 金额 */
    @ApiModelProperty(value = "金额",required = true)
    private Integer amount;

}
