package com.chargebolt.hera.client.dto.common;

public class RentalAuthConst {

    /** 
     * 租借授权记录锁前缀 
     * key = "__RENTAL_AUTH_LOCK_" + payAuthNo
     * value = "orderNo"
     * 在创建订单时添加此标记，在手动支付订单或授权单取消和失效时清理此缓存标记。
     * 在前端租借弹宝时，校验此标记，如果存在则提示用户订单已创建。
    */
    public static final String RENTAL_AUTH_LOCK_PREFIX = "__RENTAL_AUTH_LOCK_";

    /** 
     * TODO 这是临时方案，后续灰度放开后，无需设置和判断此标记
     * 租借授权记录检查锁前缀 
     * key = "__RENTAL_AUTH_CHECK_LOCK_" + payAuthNo
     * value = "orderNo"
     * 在创建订单时添加此标记，一个月后失效。
     * 在前端 check_status 时，校验此标记，如果存在则提示用户订单已创建。
    */
    public static final String RENTAL_AUTH_CHECK_LOCK_PREFIX = "__RENTAL_AUTH_CHECK_LOCK_";
}
