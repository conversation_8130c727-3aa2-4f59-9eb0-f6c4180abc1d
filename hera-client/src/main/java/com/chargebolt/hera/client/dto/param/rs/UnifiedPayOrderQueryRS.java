package com.chargebolt.hera.client.dto.param.rs;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayOrderQueryRS implements Serializable {
    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 业务交易流水号 */
    @ApiModelProperty(value = "业务交易流水号")
    private String bizTradeNo;

    /** 支付流水号 */
    @ApiModelProperty(value = "支付流水号")
    private String payTradeNo;

    /** 支付渠道流水号 */
    @ApiModelProperty(value = "支付渠道流水号")
    private String payChannelNo;

    /** 支付授权单号 */
    @ApiModelProperty(value = "支付授权单号")
    private String payAuthNo;

    /** 支付业务类型：11-支付订单,12-支付押金,23-信用卡预授权 {@link com.chargebolt.hera.client.enums.PaymentBizTypeEnum} */
    @ApiModelProperty(value = "支付业务类型：11-支付订单,12-支付押金,23-信用卡预授权 {@link com.chargebolt.hera.client.enums.PaymentBizTypeEnum}")
    private Integer bizType;

    /** 支付类型：5-微信小程序,6-钱包,7-押金抵扣 {@link com.chargebolt.hera.client.enums.PaywayEnum} */
    @ApiModelProperty(value = "支付类型：5-微信小程序,6-钱包,7-押金抵扣 {@link com.chargebolt.hera.client.enums.PaywayEnum}")
    private Integer payType;

    /** 支付方式：0-未知,201-支付宝,203-微信 {@link com.chargebolt.hera.client.enums.PayMethodEnum} */
    @ApiModelProperty(value = "支付方式：0-未知,201-支付宝,203-微信 {@link com.chargebolt.hera.client.enums.PayMethodEnum}")
    private Integer payMethod;

    /** 客户端类型：1-普通h5,2-微信小程序,4-安卓客户端,5-iOS客户端 {@link com.chargebolt.eden.enums.ClientTypeEnum} */
    @ApiModelProperty(value = "客户端类型：1-普通h5,2-微信小程序,4-安卓客户端,5-iOS客户端 {@link com.chargebolt.eden.enums.ClientTypeEnum}")
    private Integer clientType;

    /** 代理商ID */
    @ApiModelProperty(value = "代理商ID")
    private Long tenantId;

    /** 支付渠道配置记录主键ID */
    @ApiModelProperty(value = "支付渠道配置记录主键ID")
    private Long payConfigId;

    /** 状态：1-初始化,2-已支付,6-退款中,7-已退款,8-退款失败,9-支付取消,99-失败 {@link com.chargebolt.hera.client.enums.status.PayStatus} */
    @ApiModelProperty(value = "状态：1-初始化,2-已支付,6-退款中,7-已退款,8-退款失败,9-支付取消,99-失败 {@link com.chargebolt.hera.client.enums.status.PayStatus}")
    private Integer status;

    /** 支付金额，实际支付（转汇后的金额） */
    @ApiModelProperty(value = "支付金额，实际支付（转汇后的金额）")
    private Integer payAmount;

    /** 退款金额，实际退款（转汇后的金额） */
    @ApiModelProperty(value = "退款金额，实际退款（转汇后的金额）")
    private Integer refundAmount;

    /** 渠道优惠金额 */
    @ApiModelProperty(value = "渠道优惠金额")
    private Integer channelDiscountAmount;

    /** 币种: 默认人民币 */
    @ApiModelProperty(value = "币种: 默认人民币")
    private String currency;

    /** 支付成功时间 */
    @ApiModelProperty(value = "支付成功时间")
    private Date payTime;

    /** 退款成功时间 */
    @ApiModelProperty(value = "退款成功时间")
    private Date refundTime;

    /** 扩展字段 */
    @ApiModelProperty(value = "扩展字段")
    private String jsonExtensions;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 创建时间戳 */
    @ApiModelProperty(value = "创建时间戳")
    private Long gmtCreate;

    /** 更新时间戳 */
    @ApiModelProperty(value = "更新时间戳")
    private Long gmtUpdate;

}
