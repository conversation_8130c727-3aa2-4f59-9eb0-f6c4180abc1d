package com.chargebolt.hera.client.apiv2;

import com.chargebolt.hera.client.dto.param.rq.UnifiedPayOrderPageQuery;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayOrderQueryRS;
import com.chargebolt.hera.client.dto.pay.QueryPageDTO;
import com.chargebolt.hera.client.dto.pay.QueryPageResultDTO;
import org.springframework.web.bind.annotation.*;
import so.dian.mofa3.lang.domain.Result;

public interface UnifiedPayQueryApi {

    /**
     * 统一支付订单根据支付交易单号查询
     * @param payTradeNo 支付交易单号
     * @return
     */
    @GetMapping("/api/pay/qry/unifiedOrderByPayTradeNo")
    Result<UnifiedPayOrderQueryRS> unifiedOrderQryByPayTradeNo(@RequestParam("payTradeNo") String payTradeNo);

    /**
     * 统一支付订单根据业务交易单号查询
     * @param bizTradeNo 业务交易单号
     * @return
     */
    @GetMapping("/api/pay/qry/unifiedOrderByBizTradeNo")
    Result<UnifiedPayOrderQueryRS> unifiedOrderQryByBizTradeNo(@RequestParam("bizTradeNo") String bizTradeNo);

    /**
     * 统一支付订单根据渠道支付单号查询
     * @param payChannelNo 渠道支付单号
     * @return
     */
    @GetMapping("/api/pay/qry/unifiedOrderByPayChannelNo")
    Result<UnifiedPayOrderQueryRS> unifiedOrderQryByPayChannelNo(@RequestParam("payChannelNo") String payChannelNo);

    /**
     * 查询原支付凭证DTO
     * 使用场景：oss端押金列表 和 预授金管理列表 查询
     * TODO 数据取自 同步库
     * @param rq 分页查询条件
     */
    @PostMapping("/api/pay/qry/unifiedOrderByPage")
    Result<QueryPageResultDTO<UnifiedPayOrderQueryRS>> unifiedOrderQryByPage(@RequestBody QueryPageDTO<UnifiedPayOrderPageQuery> rq);

}
