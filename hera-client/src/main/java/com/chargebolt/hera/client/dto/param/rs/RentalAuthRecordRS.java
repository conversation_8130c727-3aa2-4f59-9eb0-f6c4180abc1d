/*
 * Copyright (c) 2016-2025 Chargebolt
 *
 * This code is proprietary software and may only be accessed by authorized users.
 * Any unauthorized use is considered an infringement.
 *
 */
package com.chargebolt.hera.client.dto.param.rs;

import java.io.Serializable;

import com.chargebolt.hera.client.enums.PayAuthTypeEnum;

import lombok.Data;

/*
 * @file       RentalAuthRecordRS.java
 * <AUTHOR>
 * @date       2025/8/21 18:37
 * @details    [Detailed notes]
 *
 * MODIFICATION HISTORY:
 * - [Date]     [Modifier]: [Description of Change]
 * -
 */
@Data
public class RentalAuthRecordRS implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202508233183753L;
    /** 主键，唯一标识每条记录 */
    private Long id;

    /** 用户ID，关联用户表 */
    private Long userId;

    /** 代理商ID */
    private Long tenantId;

    /** 租借授权凭证编号 */
    private String payAuthNo;

    /** 租借订单编号 */
    private String rentalOrderNo;

    /** 支付授权类型（1：押金，2：预授权，3：代扣）{@link PayAuthTypeEnum} */
    private Integer payAuthType;

    /** 授权金额（押金金额、预授权金额、代扣金额） */
    private Long amount;

    /** 货币类型（如CNY、USD等） */
    private String currency;

    /** 状态（根据记录类型不同含义不同）{@link com.chargebolt.hera.client.enums.status.RentalAuthStatus} */
    private Integer status;

    /** 支付渠道（如支付宝、微信、银行卡等）{@link com.chargebolt.hera.client.enums.PaywayEnum} */
    private Integer payType;

    /** 支付方式（如余额、支付宝、微信、银行卡等）{@link com.chargebolt.hera.client.enums.PayMethodEnum} */
    private Integer payMethod;

    /** 支付渠道配置记录主键ID */
    private Long payConfigId;

    /** 当前关联订单编号 */
    private String bizAuthNo;

    /**
     * 预授权时，是三方单号
     * 代扣时，是三方单号
     * 押金充值时，是押金充值支付单号
     * */
    private String channelAuthNo;

    /** 扩展字段，用于存储其他非通用信息 {@link com.chargebolt.hera.domain.PaymentAuthJsonExtensions }*/
    private String jsonExtensions;

    /** 备注信息 */
    private String remark;

    /** 创建时间 */
    private Long createTimeStamp;

    /**
     * 授权成功时间
     */
    private Long authTimeStamp;

    /** 过期时间 */
    private Long expireTimeStamp;

}