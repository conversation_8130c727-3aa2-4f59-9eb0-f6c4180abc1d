package com.chargebolt.hera.client.apiv2;

import com.chargebolt.hera.client.dto.param.rq.UnifiedPayCloseRQ;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayCloseRS;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.mofa3.lang.domain.Result;

public interface UnifiedPayCloseApi {

    /**
     * 统一支付关闭（取消）接口
     * 支付单创建时，给渠道关单时间统一是15分钟，业务放调用此接口时需要确保订单已经在创建时间的15分钟后
     * @param rq
     * @return
     */
    @PostMapping("/api/pay/unifiedPayClose")
    Result<UnifiedPayCloseRS> unifiedPayClose(@RequestBody UnifiedPayCloseRQ rq);

}
