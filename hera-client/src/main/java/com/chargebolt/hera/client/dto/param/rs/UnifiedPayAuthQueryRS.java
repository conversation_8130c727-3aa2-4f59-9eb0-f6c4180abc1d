package com.chargebolt.hera.client.dto.param.rs;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayAuthQueryRS implements Serializable {


    /** 商户授权单号 */
    private String rentAuthNo;

    /** 渠道授权单号 */
    private String channelAuthNo;

    /** 订单状态，三方结果需要转换为订单状态 {@link com.chargebolt.hera.client.enums.status.RentalAuthStatus} */
    private Integer status;

    /** 交易成功时间 */
    private Date payTime;

    /** 失败信息  */
    private String failReason;

    /** 渠道是否支持订单查询，默认支持查询 */
    private boolean channelSupportQuery;

    public UnifiedPayAuthQueryRS(boolean channelSupportQuery){
        this.channelSupportQuery = channelSupportQuery;
    }

    public UnifiedPayAuthQueryRS(String rentAuthNo, String channelAuthNo, Integer status,
                                 Date payTime, String failReason){
        this.channelSupportQuery = true;
        this.rentAuthNo = rentAuthNo;
        this.channelAuthNo = channelAuthNo;
        this.status = status;
        this.payTime = payTime;
        this.failReason = failReason;
    }

    /** 支付渠道返回订单查询成功 */
    public static UnifiedPayAuthQueryRS success(String rentAuthNo, String channelAuthNo, Integer status, Date payTime){
        return new UnifiedPayAuthQueryRS(rentAuthNo,channelAuthNo,status,payTime,null);
    }

    /** 支付渠道返回订单查询成功 */
    public static UnifiedPayAuthQueryRS fail(String rentAuthNo, Integer status, String failReason){
        return new UnifiedPayAuthQueryRS(rentAuthNo,null,status,null,null);
    }

    /** 支付渠道返回订单处理中 */
    public static UnifiedPayAuthQueryRS process(String rentAuthNo, Integer status){
        return new UnifiedPayAuthQueryRS(rentAuthNo,null,status,null,null);
    }

    /** 支付渠道不支持订单查询 */
    public static UnifiedPayAuthQueryRS unSupport(){
        return new UnifiedPayAuthQueryRS(false);
    }

}
