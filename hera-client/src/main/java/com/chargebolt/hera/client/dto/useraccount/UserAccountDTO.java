package com.chargebolt.hera.client.dto.useraccount;

import com.chargebolt.hera.client.enums.account.AccountTypeEnum;
import com.chargebolt.hera.client.enums.account.UserAccountStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class UserAccountDTO implements Serializable {

    /** 主键id */
    @ApiModelProperty(value = "账户ID",required = true)
    private Long id;

    /** 用户id */
    @ApiModelProperty(value = "用户id",required = true)
    private Long userId;

    /** 代理商ID */
    @ApiModelProperty(value = "代理商ID",required = true)
    private Long tenantId;

    /** 账户类型：1-钱包账户；2-押金账户；3-积分账户；4-赠送金额账户 {@link AccountTypeEnum} */
    @ApiModelProperty(value = "账户类型：1-钱包账户；2-押金账户；3-积分账户；4-赠送金额账户",required = true)
    private int accountType;

    /** 账户总金额 */
    @ApiModelProperty(value = "账户总金额",required = true)
    private Integer totalAmount;

    /** 账户可用金额 */
    @ApiModelProperty(value = "账户可用金额",required = true)
    private Integer availableAmount;

    /** 账户冻结金额 */
    @ApiModelProperty(value = "账户冻结金额",required = true)
    private Integer frozenAmount;

    /** 币种 */
    @ApiModelProperty(value = "币种",required = true)
    private String currencyCode;

    /** 账户状态 -1-失效；1-正常；2-冻结 {@link UserAccountStatusEnum } */
    @ApiModelProperty(value = "账户状态 -1-失效；1-正常；2-冻结",required = true)
    private int status;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间",required = true)
    private Date createTime;
}
