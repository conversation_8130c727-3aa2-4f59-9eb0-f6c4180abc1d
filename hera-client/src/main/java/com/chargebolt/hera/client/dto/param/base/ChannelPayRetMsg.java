package com.chargebolt.hera.client.dto.param.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChannelPayRetMsg extends ChannelRetMsg {

    /** 渠道用户标识 **/
    @ApiModelProperty(value = "渠道用户标识")
    private String channelUserId;

    /** 是否需要轮询查单（比如微信条码支付） 默认不查询订单 **/
    @ApiModelProperty(value = "是否需要轮询查单")
    private boolean isNeedQuery = false;

    /** 渠道支付数据包, 一般用于支付订单的继续支付操作 **/
    @ApiModelProperty(value = "渠道支付数据包")
    private String channelAttach;

    // 静态初始函数
    public ChannelPayRetMsg() {
    }

    public ChannelPayRetMsg(ChannelState channelState, String channelOrderId, String channelErrCode,
            String channelErrMsg) {
        this.setChannelState(channelState);
        this.setChannelOrderId(channelOrderId);
        this.setChannelErrCode(channelErrCode);
        this.setChannelErrMsg(channelErrMsg);
    }

    /** 明确处理中，待用户支付 **/
    public static ChannelPayRetMsg confirmWaiting(String channelOrderId) {
        return new ChannelPayRetMsg(ChannelState.WAITING, channelOrderId, null, null);
    }

    /** 明确成功 **/
    public static ChannelPayRetMsg confirmSuccess(String channelOrderId) {
        return new ChannelPayRetMsg(ChannelState.CONFIRM_SUCCESS, channelOrderId, null, null);
    }

    /** 明确失败 **/
    public static ChannelPayRetMsg confirmFail(String channelErrCode, String channelErrMsg) {
        return new ChannelPayRetMsg(ChannelState.CONFIRM_FAIL, null, channelErrCode, channelErrMsg);
    }

    /** 明确失败 **/
    public static ChannelPayRetMsg confirmFail() {
        return new ChannelPayRetMsg(ChannelState.CONFIRM_FAIL, null, null, null);
    }

}
