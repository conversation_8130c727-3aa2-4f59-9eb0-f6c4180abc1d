package com.chargebolt.hera.client.dto.param.rq;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CurrencyTransferInfo {
    /**
     * 当前币种
     */
    @ApiModelProperty(value = "当前币种",required = true)
    private String currentCurrency;
    /**
     * 目标币种
     */
    @ApiModelProperty(value = "目标币种",required = true)
    private String targetCurrency;

    /**
     * 币种兑换倍率
     */
    @ApiModelProperty(value = "币种兑换倍率",required = true)
    private Double exchangeRate;

}
