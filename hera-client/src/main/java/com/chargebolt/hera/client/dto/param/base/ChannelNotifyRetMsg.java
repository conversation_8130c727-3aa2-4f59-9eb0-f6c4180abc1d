package com.chargebolt.hera.client.dto.param.base;

import org.springframework.http.ResponseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChannelNotifyRetMsg extends ChannelRetMsg {

    /** 渠道用户标识 **/
    @ApiModelProperty(value = "渠道用户标识")
    private String channelUserId;

    /** 响应结果（一般用于回调接口返回给上游数据 ） **/
    @ApiModelProperty(value = "响应结果")
    private ResponseEntity responseEntity;

    // 静态初始函数
    public ChannelNotifyRetMsg() {
    }

    public ChannelNotifyRetMsg(ChannelState channelState, String channelOrderId, String channelErrCode,
            String channelErrMsg) {
        this.setChannelState(channelState);
        this.setChannelOrderId(channelOrderId);
        this.setChannelErrCode(channelErrCode);
        this.setChannelErrMsg(channelErrMsg);
    }

    /** 明确处理中，待用户支付 **/
    public static ChannelNotifyRetMsg confirmWaiting(String channelOrderId) {
        return new ChannelNotifyRetMsg(ChannelState.WAITING, channelOrderId, null, null);
    }

    /** 明确成功 **/
    public static ChannelNotifyRetMsg confirmSuccess(String channelOrderId) {
        return new ChannelNotifyRetMsg(ChannelState.CONFIRM_SUCCESS, channelOrderId, null, null);
    }

    /** 明确失败 **/
    public static ChannelNotifyRetMsg confirmFail(String channelErrCode, String channelErrMsg) {
        return new ChannelNotifyRetMsg(ChannelState.CONFIRM_FAIL, null, channelErrCode, channelErrMsg);
    }

    /** 明确失败 **/
    public static ChannelNotifyRetMsg confirmFail() {
        return new ChannelNotifyRetMsg(ChannelState.CONFIRM_FAIL, null, null, null);
    }
}
