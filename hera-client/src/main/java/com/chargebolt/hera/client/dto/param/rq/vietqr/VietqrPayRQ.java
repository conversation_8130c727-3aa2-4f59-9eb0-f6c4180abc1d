package com.chargebolt.hera.client.dto.param.rq.vietqr;

import com.chargebolt.hera.client.dto.param.rq.UnifiedPayRQ;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VietqrPayRQ extends UnifiedPayRQ {

    /**
     * 渠道支付单号
     */
    @ApiModelProperty(value = "渠道支付单号",required = true)
    private String transactionId;

    /**
     * 关联单号，退款场景使用
     */
    @ApiModelProperty(value = "关联单号，退款场景使用",required = true)
    private String referenceNumber;

    /**
     * 用户银行账户
     */
    @ApiModelProperty(value = "用户银行账户",required = true)
    private String bankAccount;

    /**
     * 支付流水号-随机生成
     */
    @ApiModelProperty(value = "支付流水号-随机生成",required = true)
    private String reftransactionid;

}
