package com.chargebolt.hera.client.dto.notify.v2;

import lombok.Data;

@Data
public class PaymentNotifyDTO {

    /**
     * 支付单号
     */
    private String payTradeNo;

    /**
     * 业务交易单号
     */
    private String bizTradeNo;

    /**
     * 业务类型
     * @PaymentBizTypeEnum
     */
    private Integer bizType;

    /**
     * 支付方式
     * @PaywayEnum
     */
    private Integer payway;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 支付金额
     */
    private Long amount;

    /**
     * 支付时间
     */
    private Long payTime;

    /**
     * 设备号
     */
    private String deviceNo;

    /**
     * 支付方式
     * @PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 货币
     */
    private String currency;

    /**
     * 支付状态
     * @PayStatusEnum
     */
    private Integer payStatus;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 支付授权单号
     */
    private String payAuthNo;
}
