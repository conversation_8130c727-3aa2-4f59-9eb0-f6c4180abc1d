/*
 * Copyright (c) 2016-2025 Chargebolt
 *
 * This code is proprietary software and may only be accessed by authorized users.
 * Any unauthorized use is considered an infringement.
 *
 */
package com.chargebolt.hera.client.dto.param.base;

import lombok.Data;

import java.io.Serializable;

/*
 * @file       PageRequest.java
 * <AUTHOR>
 * @date       2025/8/21 18:13
 * @details    分页请求参数
 *
 * MODIFICATION HISTORY:
 * - [Date]     [Modifier]: [Description of Change]
 * -
 */
@Data
public class PageRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202508233181324L;

    private Integer pageNum=1;
    private Integer pageSize=20;

}