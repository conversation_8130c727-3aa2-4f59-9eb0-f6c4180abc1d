package com.chargebolt.hera.client.dto.param.rq.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UserAccountQueryRQ implements Serializable {

    @ApiModelProperty(value = "租户id",required = true)
    private Long tenantId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "账户类型：1-钱包账户；2-押金账户；3-积分账户；4-赠送金额账户 {@link AccountTypeEnum}",required = true)
    private Integer accountType;
}
