package com.chargebolt.hera.client.dto.param.rq;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.rq.antom.AntomAuthRQ;
import com.chargebolt.hera.client.enums.PayAuthTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UnifiedPayAuthRQ extends UnifiedPayBase {

    /** 支付授权类型  {@link PayAuthTypeEnum} */
    @ApiModelProperty(value = "支付授权类型",required = true)
    private Integer payAuthType;

    @ApiModelProperty(value = "转汇信息")
    private CurrencyTransferInfo currencyTransferInfo;

    /**
     * 构建业务参数
     * @return 请求参数
     */
    public UnifiedPayAuthRQ buildBizRQ() {
        PaywayEnum paywayEnum = PaywayEnum.explain(this.payWay);
        if(paywayEnum == null){
            return this;
        }
        switch (paywayEnum){
            case ANTOM_CHECKOUT_CARD:
                AntomAuthRQ antomAuthRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"), AntomAuthRQ.class);
                BeanUtils.copyProperties(this, antomAuthRQ);
                return antomAuthRQ;
            default:
                return this;
        }
    }
}
