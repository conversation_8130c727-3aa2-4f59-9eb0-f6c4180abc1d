package com.chargebolt.hera.client.dto.param.rs;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayQueryRS implements Serializable {

    /** 支付流水号 */
    private String payTradeNo;

    /** 支付渠道流水号 */
    private String payChannelNo;

    /** 订单状态，三方结果需要转换为订单状态 {@link com.chargebolt.hera.client.enums.status.PayStatus} */
    private Integer status;

    /** 交易成功时间 */
    private Date payTime;

    /** 失败信息  */
    private String failReason;

    /** 渠道是否支持订单查询，默认支持查询 */
    private boolean channelSupportQuery;

    public UnifiedPayQueryRS(){}

    public UnifiedPayQueryRS(boolean channelSupportQuery){
        this.channelSupportQuery = channelSupportQuery;
    }

    public UnifiedPayQueryRS(String payTradeNo, String payChannelNo, Integer status,
                             Date payTime, String failReason){
        this.channelSupportQuery = true;
        this.payTradeNo = payTradeNo;
        this.payChannelNo = payChannelNo;
        this.status = status;
        this.payTime = payTime;
        this.failReason = failReason;
    }

    /** 支付渠道返回订单查询成功 */
    public static UnifiedPayQueryRS success(String payTradeNo, String payChannelNo, Integer status, Date payTime){
        return new UnifiedPayQueryRS(payTradeNo,payChannelNo,status,payTime,null);
    }

    /** 支付渠道返回订单查询成功 */
    public static UnifiedPayQueryRS fail(String payTradeNo, Integer status, String failReason){
        return new UnifiedPayQueryRS(payTradeNo,null,status,null,failReason);
    }

    /** 支付渠道返回订单处理中 */
    public static UnifiedPayQueryRS process(String payTradeNo, Integer status){
        return new UnifiedPayQueryRS(payTradeNo,null,status,null,null);
    }

    /** 支付渠道不支持订单查询 */
    public static UnifiedPayQueryRS unSupport(){
        return new UnifiedPayQueryRS(false);
    }

}
