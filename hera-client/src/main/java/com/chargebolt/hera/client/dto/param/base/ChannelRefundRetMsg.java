package com.chargebolt.hera.client.dto.param.base;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChannelRefundRetMsg extends ChannelRetMsg {

    // 静态初始函数
    public ChannelRefundRetMsg() {
    }

    public ChannelRefundRetMsg(ChannelState channelState, String channelOrderId, String channelErrCode,
            String channelErrMsg) {
        this.setChannelState(channelState);
        this.setChannelOrderId(channelOrderId);
        this.setChannelErrCode(channelErrCode);
        this.setChannelErrMsg(channelErrMsg);
    }

    /** 明确处理中，待用户支付 **/
    public static ChannelRefundRetMsg confirmWaiting(String channelOrderId) {
        return new ChannelRefundRetMsg(ChannelState.WAITING, channelOrderId, null, null);
    }

    /** 明确成功 **/
    public static ChannelRefundRetMsg confirmSuccess(String channelOrderId) {
        return new ChannelRefundRetMsg(ChannelState.CONFIRM_SUCCESS, channelOrderId, null, null);
    }

    /** 明确失败 **/
    public static ChannelRefundRetMsg confirmFail(String channelErrCode, String channelErrMsg) {
        return new ChannelRefundRetMsg(ChannelState.CONFIRM_FAIL, null, channelErrCode, channelErrMsg);
    }

    /** 明确失败 **/
    public static ChannelRefundRetMsg confirmFail() {
        return new ChannelRefundRetMsg(ChannelState.CONFIRM_FAIL, null, null, null);
    }
}
