package com.chargebolt.hera.client.dto.param.rq;

import com.chargebolt.hera.client.enums.PaywayEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.rq.airwallex.AirwallexCapturePayRQ;
import com.chargebolt.hera.client.dto.param.rq.antom.AntomCapturePayRQ;
import com.chargebolt.hera.client.dto.param.rq.antom.AntomPayRQ;
import com.chargebolt.hera.client.dto.param.rq.ifortepay.IfortePayRQ;
import com.chargebolt.hera.client.dto.param.rq.midtrans.MidtransRQ;
import com.chargebolt.hera.client.dto.param.rq.pingpong.PingPongRQ;
import com.chargebolt.hera.client.dto.param.rq.vietqr.VietqrPayRQ;
import com.chargebolt.hera.client.dto.param.rq.wechat.WechatPayRQ;
import com.chargebolt.hera.client.dto.param.rq.zalopay.ZaloPayRQ;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class UnifiedPayRQ extends UnifiedPayBase {

    /** 转汇信息 必填：否  目前 hk微信小程序 和 antom 支付宝小程序 */
    @ApiModelProperty(value = "转汇信息")
    private CurrencyTransferInfo currencyTransferInfo;

    /**
     * 支付场景
     * 1、普通支付
     * 2、押金抵扣
     * 3、预授权支付（请款）
     * @see com.chargebolt.hera.client.enums.PayCaseEnum
     */
    @ApiModelProperty(value = "支付场景",required = true)
    private Integer payCase;

    /**
     * 授权三方单号，类似三方支付单号，
     * 订单支付时必传
     * 对应 rental_auth_record 表中的 channel_auth_no
     * PayCase = 2 和 3 时，必填
     * payAuthType = 1(押金) 时，channelAuthNo 为押金支付单号。其他授权类型时，为三方渠道授权单号。
     * 此参数由调用方先查询授权凭证，再将授权凭证的 channelAuthNo 赋值给此参数。
     */
    @ApiModelProperty(value = "授权三方单号")
    private String channelAuthNo;

    /**
     * 支付重定向URL
     * 订单支付场景下，指定支付重定向URL，用于支付成功后跳转
     */
    @ApiModelProperty(value = "支付重定向URL")
    private String payRedirectUrl;


    /**
     * 构建业务参数
     * @return 请求参数 指定的 payway，如遇到预授权请款场景，需要用授权凭证的 payway
     */
    public UnifiedPayRQ buildBizRQ(Integer payWay) {
        PaywayEnum paywayEnum = PaywayEnum.explain(payWay);
        switch (paywayEnum){
            case WECHAT_MINIPROGRAM:
                WechatPayRQ wechatPayRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"),
                        WechatPayRQ.class);
                BeanUtils.copyProperties(this, wechatPayRQ);
                return wechatPayRQ;
            case PINGPONG_CHECKOUT_APM:
                PingPongRQ pingPongRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"),
                        PingPongRQ.class);
                BeanUtils.copyProperties(this, pingPongRQ);
                return pingPongRQ;
            case VIETQR_STATIC:
                VietqrPayRQ vietqrPayRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"),
                        VietqrPayRQ.class);
                BeanUtils.copyProperties(this, vietqrPayRQ);
                return vietqrPayRQ;
            case ZALOPAY:
                ZaloPayRQ zaloPayRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"),
                        ZaloPayRQ.class);
                BeanUtils.copyProperties(this, zaloPayRQ);
                return zaloPayRQ;
            case MIDTRANS_CHECKOUT_APM:
                MidtransRQ midtransRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"),
                        MidtransRQ.class);
                BeanUtils.copyProperties(this, midtransRQ);
                return midtransRQ;
            case AIRWALLEX_CHECKOUT_CARD:
                AirwallexCapturePayRQ airwallexCapturePayRQ = JSON
                        .parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"), AirwallexCapturePayRQ.class);
                BeanUtils.copyProperties(this, airwallexCapturePayRQ);
                return airwallexCapturePayRQ;
            case ANTOM_CHECKOUT_CARD:
                AntomCapturePayRQ antomCapturePayRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"),
                        AntomCapturePayRQ.class);
                BeanUtils.copyProperties(this, antomCapturePayRQ);
                return antomCapturePayRQ;
            case ANTOM_CHECKOUT_APM:
                AntomPayRQ antomPayRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"),
                        AntomPayRQ.class);
                BeanUtils.copyProperties(this, antomPayRQ);
                return antomPayRQ;
            case IFORTEPAY_CHECKOUT_APM:
                IfortePayRQ ifortePayRQ = JSON.parseObject(StringUtils.defaultIfEmpty(this.channelExtra, "{}"),
                        IfortePayRQ.class);
                BeanUtils.copyProperties(this, ifortePayRQ);
                return ifortePayRQ;
            default:
                return this;
        }
    }

}
