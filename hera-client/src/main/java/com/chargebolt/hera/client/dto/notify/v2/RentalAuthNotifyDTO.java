package com.chargebolt.hera.client.dto.notify.v2;

import java.util.Date;

import lombok.Data;

@Data
public class RentalAuthNotifyDTO {

    /** 租借授权凭证编号 */
    private String payAuthNo;

    /**
     * 状态（根据记录类型不同含义不同）
     * @RentalAuthStatus
     */
    private Integer status;

    /** 支付授权类型 */
    private Integer payAuthType;

    /** 用户ID */
    private Long userId;

    /** 租户ID */
    private Long tenantId;

    /** 设备编号 */
    private String deviceNo;

    /** 金额（分） */
    private Long amount;

    /** 币种 */
    private String currency;

    /** 授权时间 */
    private Date authTime;

    /** 支付渠道（如支付宝、微信、银行卡等）{@link com.chargebolt.hera.client.enums.PaywayEnum} */
    private Integer payType;

    /** 当前关联订单编号 */
    private String bizAuthNo;
}
