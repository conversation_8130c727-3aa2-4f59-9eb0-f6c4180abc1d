package com.chargebolt.hera.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@AllArgsConstructor
public enum PayCaseEnum {

    /** 默认/未知场景 */
    Unknown(0,"未知场景"),

    /** 普通支付 */
    PAY(1,"普通支付"),

    /** 押金抵扣 */
    DEDUCTION(2,"押金抵扣"),

    /** 预授权请款 */
    CAPTURE(3,"预授权请款"),
    ;

    private Integer id;
    private String desc;


    public static PayCaseEnum explain(Integer id) {
        if (id == null) {
            return null;
        }
        for (PayCaseEnum item : PayCaseEnum.values()) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        log.warn("No PayCaseEnum with id:" + id);
        return null;
    }
}


