package com.chargebolt.hera.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PayAuthTypeEnum {

    DEPOSIT(1, "押金"),
    CARD_PRE_AUTH(2, "银行卡预授权"),
    WX_PRE_AUTH(3, "微信信用分"),
    ZHIMA_PRE_AUTH(4, "支付宝芝麻信用分"),
    WITHHOLD(5, "代扣"),


    ;

    private Integer code;
    private String desc;

    public static PayAuthTypeEnum fromCode(Integer code) {
        for (PayAuthTypeEnum typeEnum : PayAuthTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     *  根据code判断是否是为押金预授权支付
     * @param code 支付授权类型
     * @return true/false
     */
    public static boolean isDepositAuth(Integer code) {
        return DEPOSIT.getCode().equals(code);
    }

}
