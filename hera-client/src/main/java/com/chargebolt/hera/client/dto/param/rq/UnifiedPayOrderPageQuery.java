package com.chargebolt.hera.client.dto.param.rq;

import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayOrderPageQuery implements java.io.Serializable{

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "支付交易单号")
    private String payTradeNo;

    @ApiModelProperty(value = "业务交易单号")
    private String bizTradeNo;

    @ApiModelProperty(value = "支付类型")
    private PaymentBizTypeEnum bizType;

    @ApiModelProperty(value = "支付状态")
    private Set<PayStatus> status;

}
