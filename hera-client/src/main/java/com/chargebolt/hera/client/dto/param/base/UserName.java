package com.chargebolt.hera.client.dto.param.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UserName implements Serializable {

    /**
     * 名。
     * 最大长度：32 字符
     */
    @ApiModelProperty(value = "名")
    private String firstName;

    /**
     * 中间名
     * 最大长度：32 字符
     */
    @ApiModelProperty(value = "中间名")
    private String middleName;

    /**
     * 姓
     */
    @ApiModelProperty(value = "姓")
    private String lastName;

    /**
     * 全名
     */
    @ApiModelProperty(value = "全名")
    private String fullName;
}
