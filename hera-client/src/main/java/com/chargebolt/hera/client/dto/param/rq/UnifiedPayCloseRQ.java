package com.chargebolt.hera.client.dto.param.rq;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UnifiedPayCloseRQ implements Serializable {

    /**
     * 支付流水号，与业务交易流水号二选一
     * 授权凭证这里填授权单号，payAuthNo
     * */
    @ApiModelProperty(value = "支付流水号，与业务交易流水号二选一",required = false)
    private String payTradeNo;

    /** 业务类型 {@link com.chargebolt.hera.client.enums.PaymentBizTypeEnum} */
    @ApiModelProperty(value = "业务类型",required = true)
    protected Integer bizType;


}
