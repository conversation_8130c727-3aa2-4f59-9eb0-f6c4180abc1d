package so.dian.hera.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 退款服务配置
 *
 * 用于控制新旧退款逻辑的切换和相关功能开关
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "hera.refund")
public class RefundServiceConfig {

    /**
     * 是否启用简化版退款服务
     * true: 使用新的四步骤架构
     * false: 使用原有的策略模式架构
     */
    private boolean enableSimplifiedService = false;

    /**
     * 简化版服务的灰度比例 (0-100)
     * 只有在enableSimplifiedService=true时生效
     * 用于逐步切换到新架构
     */
    private int simplifiedServiceGrayRatio = 0;

    /**
     * 是否启用详细日志
     * 用于监控新架构的运行情况
     */
    private boolean enableDetailedLogging = true;

    /**
     * 是否启用性能监控
     * 用于比较新旧架构的性能差异
     */
    private boolean enablePerformanceMonitoring = true;

    /**
     * 新架构的超时配置 (毫秒)
     * 每个步骤的最大执行时间
     */
    private long stepTimeoutMs = 30000;

    /**
     * 是否在新架构失败时自动回退到旧架构
     * 用于保障系统稳定性
     */
    private boolean enableFallbackToOldService = true;

    /**
     * 回退阈值：连续失败多少次后触发回退
     */
    private int fallbackFailureThreshold = 3;

    /**
     * 回退后的恢复检查间隔 (分钟)
     */
    private int fallbackRecoveryCheckIntervalMinutes = 5;
}