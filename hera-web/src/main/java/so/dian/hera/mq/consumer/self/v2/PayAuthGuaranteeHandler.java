package so.dian.hera.mq.consumer.self.v2;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.mq.consumer.self.MqHandler;
import so.dian.hera.service.v2.UnifiedPayAuthService;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PayAuthGuaranteeHandler implements MqHandler {

    @Value("${notify.rocketmq.self.tag.payAuthGuarantee}")
    private String payAuthGuaranteeTag;

    @Resource
    private UnifiedPayAuthService unifiedPayAuthService ;

    @Override
    public Boolean handle(String msgBody) {
        return unifiedPayAuthService.payAuthGuarantee(msgBody);
    }

    @Override
    public String subscribeTag() {
        return payAuthGuaranteeTag;
    }

}
