package so.dian.hera.mq.consumer.self.v2;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.refund.v2.RefundRQ;
import com.chargebolt.hera.domain.notify.DelayRefundV2Body;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.mq.consumer.self.MqHandler;
import so.dian.hera.service.v2.UnifiedRefundService;
import so.dian.mofa3.lang.domain.Result;
import javax.annotation.Resource;

/**
 * v2 延迟退款消息处理器：消费延迟退款消息，并调用统一退款入口
 */
@Slf4j
@Component
public class RefundDelayInitiateHandlerV2 implements MqHandler {

    @Value("${notify.rocketmq.self.tag.refund-delay-initiate-v2:refund-delay-initiate-v2}")
    private String refundDelayInitiateV2;

    @Resource
    private UnifiedRefundService unifiedRefundService;

    @Override
    public Boolean handle(String msgBody) {
        log.info("[V2]延迟退款消息消费: {}", msgBody);
        try {
            DelayRefundV2Body body = JSON.parseObject(msgBody, DelayRefundV2Body.class);
            if (body == null || StringUtils.isBlank(body.getBizTradeNo()) || body.getAmount() == null) {
                log.error("[V2]延迟退款消息体内容错误,丢弃: {}", msgBody);
                return Boolean.TRUE;
            }

            RefundRQ refundRQ = new RefundRQ();
            refundRQ.setBizTradeNo(body.getBizTradeNo());
            refundRQ.setRefundReason(body.getRefundReason());
            refundRQ.setAmount(body.getAmount());
            refundRQ.setSystem(body.getSystem());
            // 标记这是由延迟任务触发的退款，并透传服务费与用户ID（如有）
            refundRQ.setDelayTask(Boolean.TRUE);
            refundRQ.setUserId(body.getUserId());

            Result<?> rs = unifiedRefundService.refund(refundRQ);
            log.info("[V2]延迟退款发起完成，bizTradeNo={}, result={}", refundRQ.getBizTradeNo(), JSON.toJSONString(rs));
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[V2]延迟退款发起失败, msgBody={}", msgBody, e);
            return Boolean.FALSE;
        }
    }

    @Override
    public String subscribeTag() {
        return refundDelayInitiateV2;
    }
}


