package so.dian.hera.mq.consumer.self.v2;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import com.chargebolt.hera.domain.TenantPaymentConfig;
import com.chargebolt.hera.domain.notify.v2.RefundNotifyBodyV2;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.PaymentOrderDAO;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.hera.interceptor.v2.IRefundService;
import so.dian.platform.common.mq.producer.v2.body.RefundGuaranteeMsgBody;
import so.dian.hera.mq.consumer.self.MqHandler;
import so.dian.hera.service.paychannel.TenantPaymentConfigService;
import so.dian.hera.service.v2.BeanContextHelper;
import so.dian.hera.service.v2.business.RefundFailureProcessor;
import so.dian.hera.service.v2.business.RefundSuccessProcessor;
import so.dian.platform.common.constants.ServiceNameConstants;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.ValidateUtil;

/**
 * 退款保障消息处理器
 * 
 * 作用：回查三方退款渠道订单退款结果，避免订单退款成功，但退款结果未同步到Hera
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefundGuaranteeHandler implements MqHandler {

    @Value("${notify.rocketmq.self.tag.refundGuarantee}")
    private String refundGuaranteeTag;

    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;
    @Resource
    private PaymentOrderDAO paymentOrderMapper;
    @Resource
    private TenantPaymentConfigService tenantPaymentConfigService;
    @Resource
    private BeanContextHelper beanContextHelper;
    @Resource
    private RefundSuccessProcessor refundSuccessProcessor;
    @Resource
    private RefundFailureProcessor refundFailureProcessor;

    @Override
    public Boolean handle(String msgBody) {
        log.info("退款保障消息处理开始，msgBody: {}", msgBody);

        try {
            RefundGuaranteeMsgBody guaranteeMsgBody = JSON.parseObject(msgBody, RefundGuaranteeMsgBody.class);
            return processRefundGuarantee(guaranteeMsgBody);
        } catch (Exception e) {
            log.error("退款保障消息处理异常，msgBody: {}", msgBody, e);
            return false; // 消费失败，触发重试
        }
    }

    @Override
    public String subscribeTag() {
        return refundGuaranteeTag;
    }

    /**
     * 处理退款保障逻辑
     */
    private Boolean processRefundGuarantee(RefundGuaranteeMsgBody guaranteeMsgBody) {
        String refundNo = guaranteeMsgBody.getRefundNo();
        log.info("退款保障处理开始，refundNo: {}", refundNo);

        // 1. 查询退款单状态
        RefundDO refundDO = refundMapper.select(refundNo);
        if (refundDO == null) {
            log.warn("退款单不存在，refundNo: {}", refundNo);
            return true; // 退款单不存在，消费成功
        }

        // 2. 检查退款单状态，只处理初始状态和退款中状态
        if (!isRefundStatusProcessable(refundDO.getStatus())) {
            log.info("退款单已处理完成，无需保障查询，refundNo: {}, status: {}", refundNo, refundDO.getStatus());
            return true; // 已终态，消费成功
        }

        // 3. 查询支付单获取配置信息
        PaymentOrderDO paymentDO = paymentOrderMapper.getPaymentByPayTradeNo(refundDO.getTradeNo());
        if (paymentDO == null) {
            log.warn("支付单不存在，payTradeNo: {}", refundDO.getTradeNo());
            return true; // 支付单不存在，消费成功
        }

        // 4. 获取支付通道配置
        TenantPaymentConfig tenantPaymentConfig = tenantPaymentConfigService
                .getRecordByIdWithCache(guaranteeMsgBody.getPayConfigId());
        if (tenantPaymentConfig == null) {
            log.warn("支付配置不存在，payConfigId: {}", paymentDO.getPayConfigId());
            return true; // 配置不存在，消费成功
        }

        // 5. 调用渠道退款查询接口
        try {
            RefundQueryResultDTO queryResult = queryRefundStatus(refundDO, paymentDO, tenantPaymentConfig);
            return processQueryResult(queryResult, refundDO);
        } catch (Exception e) {
            log.error("退款状态查询失败，refundNo: {}", refundNo, e);
            return false; // 查询失败，触发重试
        }
    }

    /**
     * 检查退款单状态是否可处理
     */
    private boolean isRefundStatusProcessable(Integer status) {
        return RefundStatus.INIT.getCode().equals(status) ||
                RefundStatus.REFUNDING.getCode().equals(status);
    }

    /**
     * 调用渠道退款查询接口
     */
    private RefundQueryResultDTO queryRefundStatus(RefundDO refundDO, PaymentOrderDO paymentDO,
            TenantPaymentConfig tenantPaymentConfig) throws Exception {
        // 如果是预授权退款，则需要使用授权凭证的 payway, 否则使用支付单的 payway
        PaywayEnum paywayEnum = PaywayEnum.explain(paymentDO.getPayType());
        if (PaywayEnum.PRE_AUTH_CAPTURE.equals(paywayEnum)) {
            RentalAuthRecordDO rentalAuthRecord = rentalAuthRecordMapper.getRentalAuthRecordByPayAuthNo(paymentDO.getPayAuthNo());
            paywayEnum = PaywayEnum.explain(rentalAuthRecord.getPayType());
        }
        ValidateUtil.requiredNotNull(paywayEnum, HeraBizErrorCodeEnum.ROUTE_EMPTY);

        // 获取退款服务实现
        IRefundService refundService = beanContextHelper.getBean(
                paywayEnum.getChannel().getChannelName() + ServiceNameConstants.REFUND_SERVICE_NAME,
                IRefundService.class);

        log.info("开始查询退款状态，refundNo: {}, payway: {}", refundDO.getRefundNo(), paymentDO.getPayType());
        return refundService.query(refundDO, tenantPaymentConfig, paymentDO);
    }

    /**
     * 处理查询结果
     */
    private Boolean processQueryResult(RefundQueryResultDTO queryResult, RefundDO refundDO) {
        log.info("processQueryResult, refundNo: {}, queryResult: {}", refundDO.getRefundNo(), JSON.toJSONString(queryResult));
        if (queryResult == null) {
            log.warn("退款查询结果为空，refundNo: {}", refundDO.getRefundNo());
            return false; // 查询结果为空，触发重试
        }

        Integer queryStatus = queryResult.getStatus();
        log.info("退款查询结果，refundNo: {}, queryStatus: {}", refundDO.getRefundNo(), queryStatus);

        // 如果查询状态仍为处理中，返回false触发重试
        if (RefundStatus.REFUNDING.getCode().equals(queryStatus) ||
                RefundStatus.INIT.getCode().equals(queryStatus)) {
            log.info("退款仍在处理中，等待下一个周期检查，refundNo: {}, status: {}",
                    refundDO.getRefundNo(), queryStatus);
            return false; // 继续等待，触发重试
        }

        // 处理终态结果
        return processTerminalStatus(queryResult, refundDO);
    }

    /**
     * 处理终态状态
     */
    private Boolean processTerminalStatus(RefundQueryResultDTO queryResult, RefundDO refundDO) {
        try {
            Integer queryStatus = queryResult.getStatus();

            // 更新退款单基本信息
            // updateRefundBasicInfo(queryResult, refundDO);
            refundDO.setStatus(queryResult.getStatus());
            if (queryResult.getOutTraceNo() != null) {
                refundDO.setOutTraceNo(queryResult.getOutTraceNo());
            }
            if (queryResult.getRefundTime() != null) {
                refundDO.setRefundTime(queryResult.getRefundTime());
            } else if (RefundStatus.REFUNDED.getCode().equals(queryResult.getStatus())) {
                refundDO.setRefundTime(new Date());
            }

            if (RefundStatus.REFUNDED.getCode().equals(queryStatus)) {
                // 退款成功
                log.info("退款保障查询发现退款成功，refundNo: {}", refundDO.getRefundNo());
                processRefundSuccess(refundDO);
            } else if (RefundStatus.FAIL.getCode().equals(queryStatus)) {
                // 退款失败
                log.warn("退款保障查询发现退款失败，refundNo: {}", refundDO.getRefundNo());
                processRefundFailure(refundDO);
            } else {
                log.warn("未知的退款状态，refundNo: {}, status: {}", refundDO.getRefundNo(), queryStatus);
            }

            return true; // 处理完成，消费成功
        } catch (Exception e) {
            log.error("处理退款终态状态异常，refundNo: {}", refundDO.getRefundNo(), e);
            return false; // 处理异常，触发重试
        }
    }

    /**
     * 更新退款单基本信息
     */
    private void updateRefundBasicInfo(RefundQueryResultDTO queryResult, RefundDO refundDO) {
        refundDO.setStatus(queryResult.getStatus());

        if (queryResult.getOutTraceNo() != null) {
            refundDO.setOutTraceNo(queryResult.getOutTraceNo());
        }

        if (queryResult.getRefundTime() != null) {
            refundDO.setRefundTime(queryResult.getRefundTime());
        } else if (RefundStatus.REFUNDED.getCode().equals(queryResult.getStatus())) {
            refundDO.setRefundTime(new Date());
        }

        // 更新数据库
        // int updateResult = refundMapper.update(refundDO);
        // if (updateResult != 1) {
        //     log.warn("退款单状态更新失败，refundNo: {}", refundDO.getRefundNo());
        // } else {
        //     log.info("退款单状态更新成功，refundNo: {}, status: {}",
        //             refundDO.getRefundNo(), queryResult.getStatus());
        // }
    }

    /**
     * 处理退款成功
     */
    private void processRefundSuccess(RefundDO refundDO) {
        try {
            // 构建退款通知消息体
            RefundNotifyBodyV2 refundNotifyBody = buildRefundNotifyBody(refundDO, true);

            // 调用退款成功处理器
            refundSuccessProcessor.process(refundNotifyBody, null);

            log.info("退款成功处理完成，refundNo: {}", refundDO.getRefundNo());
        } catch (Exception e) {
            log.error("退款成功处理异常，refundNo: {}", refundDO.getRefundNo(), e);
            throw e; // 重新抛出异常，触发重试
        }
    }

    /**
     * 处理退款失败
     */
    private void processRefundFailure(RefundDO refundDO) {
        try {
            // 构建退款通知消息体
            RefundNotifyBodyV2 refundNotifyBody = buildRefundNotifyBody(refundDO, false);

            // 调用退款失败处理器
            refundFailureProcessor.process(refundNotifyBody, null);

            log.info("退款失败处理完成，refundNo: {}", refundDO.getRefundNo());
        } catch (Exception e) {
            log.error("退款失败处理异常，refundNo: {}", refundDO.getRefundNo(), e);
            throw e; // 重新抛出异常，触发重试
        }
    }

    /**
     * 构建退款通知消息体
     */
    private RefundNotifyBodyV2 buildRefundNotifyBody(RefundDO refundDO, boolean isSuccess) {
        RefundNotifyBodyV2 refundNotifyBody = new RefundNotifyBodyV2();
        refundNotifyBody.setMerchantRefundNo(refundDO.getRefundNo());
        refundNotifyBody.setMerchantTradeNo(refundDO.getTradeNo());
        refundNotifyBody.setOuterRefundNo(refundDO.getOutTraceNo());
        refundNotifyBody.setRefundStatus(isSuccess);
        refundNotifyBody.setRefundAmount(refundDO.getAmount());
        refundNotifyBody.setRefundTime(refundDO.getRefundTime());

        log.info("构建退款通知消息体，refundNo: {}, isSuccess: {}", refundDO.getRefundNo(), isSuccess);
        return refundNotifyBody;
    }
}