package so.dian.hera.service.v2.business;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.chargebolt.hera.client.enums.status.RentalAuthStatus;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import com.chargebolt.hera.domain.notify.BusinessEventType;
import com.chargebolt.hera.domain.notify.CallbackNotifyBody;
import com.chargebolt.hera.domain.notify.v2.PreAuthNotifyBody;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.mq.producer.v2.BizMqProducerV2;
import so.dian.platform.common.utils.ValidateUtil;

/**
 * 预授权成功事件处理器
 * 业务处理逻辑是：创建授权凭证，并发送业务通知MQ
 *
 */
@Component
@Slf4j
public class PreAuthCancelProcessor implements NotifyBusinessProcessor {

    @Resource
    private BizMqProducerV2 bizMqProducerV2;
    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;
    @Override
    public BusinessEventType getSupportedEventType() {
        return BusinessEventType.PRE_AUTH_CANCEL;
    }

    @Override
    public void process(Object payload, CallbackNotifyBody originalCallbackBody) {
        if (!(payload instanceof PreAuthNotifyBody)) {
            log.warn("错误的负载类型，期望 ChannelNotifyBody，实际为: {}", payload.getClass().getName());
            return;
        }

        PreAuthNotifyBody authBody = (PreAuthNotifyBody) payload;
        String rentalAuthNo = authBody.getRentalAuthNo();
        log.info("开始处理预授权成功业务: rentalAuthNo={}", rentalAuthNo);

        // 1. 根据 rentalAuthNo 查询租赁授权记录 (RentalAuthRecordDO)
        RentalAuthRecordDO rentalAuthRecordDO = rentalAuthRecordMapper.getRentalAuthRecordByPayAuthNo(rentalAuthNo);
        ValidateUtil.requiredNotNull(rentalAuthRecordDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        if (rentalAuthRecordDO.getStatus() != RentalAuthStatus.AUTH_SUCCESS.getCode()) {
            log.warn("租赁授权记录状态不正确，期望 AUTH_SUCCESS，实际为: {}", rentalAuthRecordDO.getStatus());
            return;
        }
        // 2. 更新租赁授权记录状态为 AUTH_CANCELLED (已取消)
        rentalAuthRecordMapper.updateStatusAndChannelAuthNo(rentalAuthRecordDO.getId(),
                RentalAuthStatus.AUTH_SUCCESS.getCode(), 
                RentalAuthStatus.AUTH_CANCELLED.getCode(),
                authBody.getDate(),
                System.currentTimeMillis(),
                authBody.getChannelAuthNo());

        log.info("预授权取消业务处理完成: rentalAuthNo={}", rentalAuthNo);
    }
}