package so.dian.hera.service.v2.auth;

import com.chargebolt.hera.domain.RentalAuthRecordDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import so.dian.hera.interceptor.v2.IPaymentAuthService;
import so.dian.hera.interceptor.v2.model.PayAuthChannelContext;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreparedAuthContext {
    /** 渠道授权服务实例 */
    private IPaymentAuthService paymentAuthService;
    /** 渠道授权上下文 */
    private PayAuthChannelContext channelContext;
    /** 本地授权记录（Tx1 创建或更新为 INIT） */
    private RentalAuthRecordDO authRecord;
}


