package so.dian.hera.service.v2.channel.auth.handlers;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;
import com.chargebolt.hera.client.enums.status.RentalAuthStatus;
import com.chargebolt.hera.domain.PaymentAuthJsonExtensions;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.hera.service.v2.channel.auth.AuthStateHandler;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.ValidateUtil;

import javax.annotation.Resource;

@Slf4j
@Component
@Order(100)
public class AuthConfirmFailHandler implements AuthStateHandler {

    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.CONFIRM_FAIL;
    }

    @Override
    public void handle(RentalAuthRecordDO record, UnifiedPayAuthRS rs) {
        PaymentAuthJsonExtensions extensions = JSON.parseObject(record.getJsonExtensions(),
                PaymentAuthJsonExtensions.class);
        String errCode = StringUtils.defaultString(rs.getChannelRetMsg().getChannelErrCode());
        String errMsg = StringUtils.defaultString(rs.getChannelRetMsg().getChannelErrMsg());
        extensions.setFailReason(errCode + ":" + errMsg);
        int result = rentalAuthRecordMapper.updateJsonExtensionsByPayAuthNo(record.getPayAuthNo(),
                RentalAuthStatus.INIT.getCode(), 
                RentalAuthStatus.AUTH_FAILED.getCode(), 
                JSON.toJSONString(extensions), 
                System.currentTimeMillis());
        ValidateUtil.requiredTrue(result > 0, HeraBizErrorCodeEnum.RENTAL_AUTH_UPDATE_FAIL);

        log.info("授权失败处理完成，rentalAuthNo={}, errCode={}, errMsg={}", record.getPayAuthNo(), errCode, errMsg);
    }
}
