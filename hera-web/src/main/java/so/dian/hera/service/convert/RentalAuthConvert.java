/*
 * Copyright (c) 2016-2025 Chargebolt
 *
 * This code is proprietary software and may only be accessed by authorized users.
 * Any unauthorized use is considered an infringement.
 *
 */
package so.dian.hera.service.convert;

import com.chargebolt.hera.client.dto.param.rs.RentalAuthRecordRS;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/*
 * @file       RentalAuthConvert.java
 * <AUTHOR>
 * @date       2025/8/21 18:22
 * @details    [Detailed notes]
 *
 * MODIFICATION HISTORY:
 * - [Date]     [Modifier]: [Description of Change]
 * -
 */
@Mapper
public interface RentalAuthConvert {
     RentalAuthConvert INSTANCE = Mappers.getMapper(RentalAuthConvert.class);
     @Mapping(source = "createTime", target = "createTimeStamp", qualifiedByName = "dateToTimestamp")
     @Mapping(source = "authTime", target = "authTimeStamp", qualifiedByName = "dateToTimestamp")
     @Mapping(source = "expireTime", target = "expireTimeStamp", qualifiedByName = "dateToTimestamp")
     RentalAuthRecordRS toRentalAuthRecordRS(RentalAuthRecordDO rentalAuthRecordDO);

     @Named("localDateTimeToTimestamp")
     default Long localDateTimeToTimestamp(LocalDateTime localDateTime) {
         return localDateTime != null ? localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : null;
     }

     @Named("dateToTimestamp")
     default Long dateToTimestamp(Date date) {
         return date != null ? date.toInstant().toEpochMilli() : null;
     }
}