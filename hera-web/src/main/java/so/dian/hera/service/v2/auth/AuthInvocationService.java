package so.dian.hera.service.v2.auth;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.interceptor.v2.IPaymentAuthService;
import so.dian.hera.interceptor.v2.model.PayAuthChannelContext;

@Slf4j
@Component
public class AuthInvocationService {

    /**
     * Tx2：非事务调用三方授权
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public UnifiedPayAuthRS invokeChannel(IPaymentAuthService paymentAuthService, PayAuthChannelContext channelContext) {
        paymentAuthService.preCheck(channelContext);
        UnifiedPayAuthRS rs = paymentAuthService.payAuth(channelContext);
        log.info("授权渠道调用完成. payAuthNo={}, channelNo={}, retMsg={}",
                channelContext.getPayAuthNo(),
                rs.getChannelAuthNo(),
                JSON.toJSONString(rs.getChannelRetMsg()));
        return rs;
    }
}


