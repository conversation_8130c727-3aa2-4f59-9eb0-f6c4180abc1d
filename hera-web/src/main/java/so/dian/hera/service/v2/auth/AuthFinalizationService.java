package so.dian.hera.service.v2.auth;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;
import com.chargebolt.hera.client.enums.status.RentalAuthStatus;
import com.chargebolt.hera.domain.PaymentAuthJsonExtensions;
import com.chargebolt.hera.domain.RentalAuthRecordDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.hera.service.v2.channel.auth.AuthChannelResponseProcessor;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.ValidateUtil;

@Slf4j
@Component
public class AuthFinalizationService {

    @Resource
    private AuthChannelResponseProcessor authChannelResponseProcessor;

    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;

    /**
     * Tx3：基于渠道返回结果持久化（新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void persistResultTx(RentalAuthRecordDO authRecord, UnifiedPayAuthRS rs) {
        authChannelResponseProcessor.process(authRecord, rs);
        ChannelRetMsg ret = rs.getChannelRetMsg();
        String statusMsg = ret == null ? null
                : ret.getChannelState() + "|" + ret.getChannelErrCode() + "|" + ret.getChannelErrMsg();
        log.info("授权渠道结果入库完成. bizTradeNo={}, payAuthNo={}, statusMsg={}",
                authRecord.getBizAuthNo(), authRecord.getPayAuthNo(), statusMsg);
    }

    /**
     * 渠道调用异常：仅记录失败原因，不改变 INIT 状态（新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void markUnknownTx(RentalAuthRecordDO authRecord, Exception ex) {
        String json = authRecord.getJsonExtensions();
        PaymentAuthJsonExtensions extensions = JSON.parseObject(json, PaymentAuthJsonExtensions.class);
        if (extensions == null) {
            extensions = new PaymentAuthJsonExtensions();
        }
        extensions.setFailReason(ex.getMessage());
        int updated = rentalAuthRecordMapper.updateJsonExtensionsByPayAuthNo(
                authRecord.getPayAuthNo(),
                RentalAuthStatus.INIT.getCode(),
                RentalAuthStatus.INIT.getCode(),
                JSON.toJSONString(extensions),
                System.currentTimeMillis());
        ValidateUtil.requiredTrue(updated > 0, HeraBizErrorCodeEnum.RENTAL_AUTH_UPDATE_FAIL);
        log.warn("授权渠道调用异常，仅记录失败原因并保持 INIT. bizTradeNo={}, payAuthNo={}, reason={}",
                authRecord.getBizAuthNo(), authRecord.getPayAuthNo(), ex.getMessage());
    }
}


