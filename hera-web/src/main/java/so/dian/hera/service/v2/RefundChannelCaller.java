package so.dian.hera.service.v2;

import static so.dian.platform.common.constants.ServiceNameConstants.REFUND_SERVICE_NAME;

import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import com.chargebolt.hera.client.dto.param.rq.RefundOrderRQ;
import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.PaymentJsonExtensions;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.PaymentVietqrMappingDO;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import com.chargebolt.hera.domain.TenantPaymentConfig;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.PaymentOrderDAO;
import so.dian.hera.dao.rds.hera.PaymentVietqrMappingMapper;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.interceptor.v2.IRefundService;
import so.dian.hera.service.paychannel.TenantPaymentConfigService;
import so.dian.hera.service.v2.enums.RefundSourceType;
import so.dian.hera.service.v2.model.PaymentRefundContext;
import so.dian.mofa3.lang.money.CurrencyEnum;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.mofa3.lang.money.MultiCurrencyMoneyUtil;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.utils.ValidateUtil;
import so.dian.platform.zalomini.processor.v2.ZaloMiniPaymentService;

/**
 * 退款渠道调用器
 *
 * 负责步骤3：【无事务】调用渠道进行退款
 * - 获取渠道配置和服务
 * - 构建退款请求参数
 * - 调用第三方退款接口
 * - 返回渠道响应结果
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefundChannelCaller {

    @Resource
    private TenantPaymentConfigService tenantPaymentConfigService;
    @Resource
    private BeanContextHelper beanContextHelper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private PaymentOrderDAO paymentOrderMapper;
    @Resource
    private ZaloMiniPaymentService zaloMiniPaymentService;
    @Resource
    private PaymentVietqrMappingMapper paymentVietqrMappingMapper;

    /**
     * 调用退款渠道（无事务操作）
     *
     * @param context  退款上下文
     * @param refundNo 退款单号
     * @return 渠道返回结果
     */
    public UnifiedRefundRS callRefundChannel(PaymentRefundContext context, String refundNo) {
        log.info("开始调用退款渠道，refundNo: {}, refundType: {}", refundNo, context.getRefundType());

        // 1. 获取退款单
        RefundDO refundDO = refundMapper.select(refundNo);
        ValidateUtil.requiredNotNull(refundDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        // 2. 根据退款类型确定实际退款的支付单
        PaymentOrderDO actualRefundPayment = determineActualRefundPayment(context);

        // 3. 获取支付通道配置
        TenantPaymentConfig tenantPaymentConfig = tenantPaymentConfigService
                .getRecordByIdWithCache(actualRefundPayment.getPayConfigId());
        ValidateUtil.requiredNotNull(tenantPaymentConfig, HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);

        PaywayEnum paywayEnum = PaywayEnum.explain(actualRefundPayment.getPayType());
        ValidateUtil.requiredNotNull(paywayEnum, HeraBizErrorCodeEnum.ROUTE_EMPTY);

        // 4. 获取退款服务实现
        IRefundService refundService = getRefundService(paywayEnum);

        // 5. 更新退款单状态为退款中
        updateRefundStatus(refundNo, RefundStatus.REFUNDING);

        // 6. 构建退款请求参数
        RefundOrderRQ refundOrderRQ = buildRefundOrderRQ(context, refundNo, actualRefundPayment,
                tenantPaymentConfig);

        // 7. 调用预检查接口
        String errMsg = refundService.preCheck(refundOrderRQ, refundDO, actualRefundPayment);
        if (StringUtils.isNotEmpty(errMsg)) {
            throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT.getCode(), errMsg);
        }

        // 8. 调用渠道退款接口（核心步骤）
        UnifiedRefundRS channelResult = refundService.refund(refundOrderRQ, tenantPaymentConfig);

        log.info("渠道退款调用完成，refundNo: {}, channelResult: {}", refundNo,
                channelResult != null ? channelResult.getRefundNo() : "null");

        return channelResult;

    }

    /**
     * 根据退款类型确定实际退款的支付单
     */
    private PaymentOrderDO determineActualRefundPayment(PaymentRefundContext context) {
        if (context.getRefundType() == RefundSourceType.DIRECT_PAYMENT) {
            return context.getTargetPayment();

        } else if (context.getRefundType() == RefundSourceType.DEPOSIT_DEDUCTION) {
            return context.getActualRefundPayment();

        } else if (context.getRefundType() == RefundSourceType.PRE_AUTH) {
            // 预授权场景：创建虚拟支付单
            return createVirtualPaymentForPreAuth(context);

        } else {
            throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);
        }
    }

    /**
     * 为预授权退款创建虚拟支付单
     */
    private PaymentOrderDO createVirtualPaymentForPreAuth(PaymentRefundContext context) {
        log.info("创建预授权退款虚拟支付单，targetPayTradeNo: {}", context.getTargetPayment().getPayTradeNo());

        PaymentOrderDO virtualPayment = new PaymentOrderDO();
        PaymentOrderDO targetPayment = context.getTargetPayment();
        RentalAuthRecordDO authRecord = context.getAuthRecord();

        // 复制基本信息
        virtualPayment.setUserId(authRecord.getUserId());
        virtualPayment.setTenantId(authRecord.getTenantId());
        virtualPayment.setPayType(authRecord.getPayType());
        virtualPayment.setPayMethod(authRecord.getPayMethod());
        virtualPayment.setCurrency(authRecord.getCurrency());
        virtualPayment.setPayConfigId(authRecord.getPayConfigId());
        virtualPayment.setBizType(targetPayment.getBizType());
        virtualPayment.setJsonExtensions(targetPayment.getJsonExtensions());

        // 设置退款相关信息
        virtualPayment.setPayAmount(targetPayment.getPayAmount());
        virtualPayment.setBizTradeNo(targetPayment.getBizTradeNo());
        virtualPayment.setPayTradeNo(targetPayment.getPayTradeNo());

        // 使用真实的三方支付单号，也就是授权记录中的三方支付单号
        virtualPayment.setPayChannelNo(authRecord.getChannelAuthNo());

        log.info("虚拟支付单创建完成，payAmount: {}, bizTradeNo: {}, channelNo: {}",
                virtualPayment.getPayAmount(), virtualPayment.getBizTradeNo(), virtualPayment.getPayChannelNo());

        return virtualPayment;
    }

    /**
     * 构建退款请求参数
     */
    private RefundOrderRQ buildRefundOrderRQ(PaymentRefundContext context, String refundNo,
            PaymentOrderDO actualRefundPayment, TenantPaymentConfig tenantPaymentConfig) {

        // 处理退款原因长度限制
        String processedReason = processRefundReason(context.getOriginalRequest().getRefundReason());

        // 构建基础退款请求
        RefundOrderRQ refundOrderRQ = new RefundOrderRQ();
        refundOrderRQ.setCurrency(actualRefundPayment.getCurrency());
        refundOrderRQ.setRefundNo(refundNo);
        refundOrderRQ.setRefundAmount(context.getRefundAmount());
        refundOrderRQ.setReason(processedReason);

        // 渠道特定参数构建
        buildChannelSpecificParams(refundOrderRQ, actualRefundPayment, context.getTargetPayment(), tenantPaymentConfig);

        // 标记是否来自延迟任务
        if (Boolean.TRUE.equals(context.getOriginalRequest().getDelayTask())) {
            refundOrderRQ.setDelayTask(Boolean.TRUE);
        }

        // 汇率转换处理
        handleCurrencyExchange(refundOrderRQ, actualRefundPayment, context);

        return refundOrderRQ;
    }

    /**
     * 处理退款原因长度限制
     */
    private String processRefundReason(String refundReason) {
        if (StringUtils.isNotEmpty(refundReason) && refundReason.length() > 80) {
            return refundReason.substring(0, 80);
        }
        return refundReason;
    }

    /**
     * 构建渠道特定参数
     */
    private void buildChannelSpecificParams(RefundOrderRQ refundOrderRQ, PaymentOrderDO actualRefundPayment,
            PaymentOrderDO requestPayment, TenantPaymentConfig tenantPaymentConfig) {

        PaywayEnum payway = PaywayEnum.explain(actualRefundPayment.getPayType());

        // 设置基本参数
        refundOrderRQ.setTradeNo(actualRefundPayment.getPayTradeNo());
        refundOrderRQ.setPayOriginalAmount(actualRefundPayment.getPayAmount());
        refundOrderRQ.setPayWay(actualRefundPayment.getPayType());
        refundOrderRQ.setPayMethod(actualRefundPayment.getPayMethod());
        refundOrderRQ.setPayNo(actualRefundPayment.getPayChannelNo());
        // Zalo Mini特殊处理
        if (PaywayEnum.ZALOPAY_MINI.getChannel().getChannelName()
                .equals(PaywayEnum.explain(actualRefundPayment.getPayType()).getChannel().getChannelName())) {
            throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT.getCode(), "ZaloPay Mini 退款不支持");
        } else if (PaywayEnum.VIETQR_STATIC == payway) {
            // VietQR 退款需要银行账户和参考号信息
            handleVietQRRefund(refundOrderRQ, requestPayment);
        }
    }

    /**
     * 处理 VietQR 退款特殊逻辑
     */
    private void handleVietQRRefund(RefundOrderRQ refundOrderRQ, PaymentOrderDO requestPayment) {
        log.info("处理 VietQR 退款，需要获取银行账户和参考号信息");
        try {
            // 查询支付单对应的 VietQR 映射信息
            PaymentVietqrMappingDO paymentVietqrMappingDO = paymentVietqrMappingMapper
                    .getRecordByTradeNo(requestPayment.getPayTradeNo());
            // 如果支付单对应的 VietQR 映射信息不存在，则抛出异常
            if (paymentVietqrMappingDO == null
                    || StringUtils.isEmpty(paymentVietqrMappingDO.getBankAccount())
                    || StringUtils.isEmpty(paymentVietqrMappingDO.getReferenceNumber())) {
                log.error("VietQR 退款缺少必要参数，tradeNo: {}", requestPayment.getPayTradeNo());
                throw new HeraBizException(HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST.getCode(), "无法退款，缺少退款必须参数");
            }
            // 设置退款请求参数
            refundOrderRQ.setBankAccount(paymentVietqrMappingDO.getBankAccount());
            refundOrderRQ.setReferenceNumber(paymentVietqrMappingDO.getReferenceNumber());
        } catch (Exception e) {
            log.error("VietQR 退款参数构建失败，payTradeNo: {}", requestPayment.getPayTradeNo(), e);
            throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT.getCode(),
                    "VietQR 退款参数构建失败: " + e.getMessage());
        }
    }

    /**
     * 汇率转换处理
     */
    private void handleCurrencyExchange(RefundOrderRQ refundOrderRQ, PaymentOrderDO actualRefundPayment,
            PaymentRefundContext context) {
        try {
            if (StringUtils.isNotBlank(actualRefundPayment.getJsonExtensions())) {
                PaymentJsonExtensions ext = JSON.parseObject(actualRefundPayment.getJsonExtensions(),
                        PaymentJsonExtensions.class);
                if (ext != null && ext.getCurrencyExchange() != null) {
                    CurrencyExchangeInfo currencyExchange = ext.getCurrencyExchange();
                    // 使用 RefundRQ.amount 作为原始币种金额
                    Long sourceAmount = context.getOriginalRequest().getAmount() != null
                            ? context.getOriginalRequest().getAmount()
                            : context.getRefundAmount();

                    MultiCurrencyMoney sourceMoney = new MultiCurrencyMoney(0, actualRefundPayment.getCurrency());
                    sourceMoney.setCent(sourceAmount);

                    MultiCurrencyMoney targetMoney = MultiCurrencyMoneyUtil.exchangeRate(sourceMoney,
                            CurrencyEnum.getByCurrencyCode(currencyExchange.getTargetCurrency()),
                            currencyExchange.getExchangeRate().doubleValue());

                    // 回填渠道请求为目标币种与金额
                    refundOrderRQ.setCurrency(currencyExchange.getTargetCurrency());
                    refundOrderRQ.setRefundAmount(Long.valueOf(String.valueOf(targetMoney.getCent())));

                    // 补充实际退款金额
                    currencyExchange.setTargetRefundAmount(refundOrderRQ.getRefundAmount().intValue());

                    // 更新 payment 扩展信息
                    paymentOrderMapper.updateExtensionsByPayTradeNo(actualRefundPayment.getPayTradeNo(),
                            JSON.toJSONString(ext), new Date(), System.currentTimeMillis());

                    log.info("汇率转换完成，原始金额: {} {}, 目标金额: {} {}",
                            sourceAmount, actualRefundPayment.getCurrency(),
                            refundOrderRQ.getRefundAmount(), refundOrderRQ.getCurrency());
                }
            }
        } catch (Exception ex) {
            log.warn("退款转汇处理失败，忽略转汇，按支付单币种退款。refundNo={}", refundOrderRQ.getRefundNo(), ex);
        }
    }

    /**
     * 更新退款单状态
     */
    private void updateRefundStatus(String refundNo, RefundStatus status) {
        try {
            RefundDO refundDO = refundMapper.select(refundNo);
            if (refundDO != null) {
                refundDO.setStatus(status.getCode());
                refundMapper.update(refundDO);
                log.info("退款单状态更新成功，refundNo: {}, status: {}", refundNo, status.getCode());
            }
        } catch (Exception e) {
            log.error("退款单状态更新失败，refundNo: {}, status: {}", refundNo, status.getCode(), e);
        }
    }

    /**
     * 获取退款服务实现
     */
    private IRefundService getRefundService(PaywayEnum paywayEnum) {
        return beanContextHelper.getBean(paywayEnum.getChannel().getChannelName() + REFUND_SERVICE_NAME,
                IRefundService.class);
    }
}