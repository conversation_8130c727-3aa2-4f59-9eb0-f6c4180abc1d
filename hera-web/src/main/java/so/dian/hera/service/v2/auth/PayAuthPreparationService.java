package so.dian.hera.service.v2.auth;

import java.util.Date;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import com.chargebolt.hera.client.dto.param.rq.CurrencyTransferInfo;
import com.chargebolt.hera.client.dto.param.rq.UnifiedPayAuthRQ;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RentalAuthStatus;
import com.chargebolt.hera.domain.PaymentAuthJsonExtensions;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import com.chargebolt.hera.domain.TenantPaymentConfig;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import org.springframework.data.redis.core.RedisTemplate;
import so.dian.hera.configuration.db.keygenerator.PrimKeyGenerator;
import static so.dian.platform.common.constants.BusinessConstants.TABLE_NAME_RENTAL_AUTH_RECORD;
import so.dian.hera.interceptor.v2.IPaymentAuthService;
import so.dian.hera.interceptor.v2.model.PayAuthChannelContext;
import so.dian.hera.service.paychannel.TenantPaymentConfigService;
import so.dian.hera.service.v2.AbstractUnifiedPay;
import so.dian.hera.service.v2.BeanContextHelper;
import so.dian.mofa3.lang.money.CurrencyEnum;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.mofa3.lang.money.MultiCurrencyMoneyUtil;
import so.dian.platform.common.constants.ServiceNameConstants;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.mq.producer.v2.SelfMqProducerV2;
import so.dian.platform.common.mq.producer.v2.body.PayAuthGuaranteeMsgBody;
import so.dian.platform.common.utils.ValidateUtil;

@Slf4j
@Component
public class PayAuthPreparationService extends AbstractUnifiedPay {

    @Resource
    private BeanContextHelper beanContextHelper;

    @Resource
    private TenantPaymentConfigService tenantPaymentConfigService;

    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;

    @Resource
    private SelfMqProducerV2 selfMqProducerV2;

    @Resource
    private Environment env;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * Tx1：准备渠道授权所需上下文并创建/更新授权记录（新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public PreparedAuthContext prepareForChannelAuthTx(UnifiedPayAuthRQ rq) {
        PaywayEnum paywayEnum = PaywayEnum.explain(rq.getPayWay());
        ValidateUtil.requiredNotNull(paywayEnum, HeraBizErrorCodeEnum.PAY_CHANNEL_NOT_SUPPORT);

        // 查询是否存在授权成功的单据，如果有则不进行授权，否则创建新的授权单
        RentalAuthRecordDO authRecordDO = rentalAuthRecordMapper.selectAuthorizedRecordByParams(
                rq.getAgentInfo().getTenantId(), rq.getCustomerInfo().getUserId(), rq.getPayAuthType());
        if (Objects.nonNull(authRecordDO)) {
            throw HeraBizException.create(HeraBizErrorCodeEnum.RENTAL_AUTH_ALREADY_EXIST);
        }

        // 获取配置
        TenantPaymentConfig config = tenantPaymentConfigService.getTenantPaymentConfig(rq.getAgentInfo().getTenantId(),
                rq.getPayWay());
        ValidateUtil.requiredNotNull(config, HeraBizErrorCodeEnum.PAY_CONFIG_NOT_EXIST);

        // 定位渠道授权服务
        IPaymentAuthService paymentAuthService = beanContextHelper.getBean(
                paywayEnum.getChannel().getChannelName() + ServiceNameConstants.PAY_AUTH_SERVICE_NAME,
                IPaymentAuthService.class);
        ValidateUtil.requiredNotNull(paymentAuthService, HeraBizErrorCodeEnum.PAY_CHANNEL_NOT_SUPPORT);

        // 构建上下文（先不含 payAuthNo，用于生成）
        PayAuthChannelContext ctxForId = PayAuthChannelContext.builder()
                .authRq(rq)
                .config(config)
                .build();
        // 创建授权单
        String payAuthNo = paymentAuthService.customPayAuthId(ctxForId);
        authRecordDO = buildAuthRecordDO(rq, payAuthNo, config.getId());
        int inserted = rentalAuthRecordMapper.insertSelective(authRecordDO);
        ValidateUtil.requiredTrue(inserted > 0, HeraBizErrorCodeEnum.RENTAL_AUTH_CREATE_FAIL);

        // 替换配置中的回调与重定向地址：授权场景用 payAuthNo
        config.setConfigDataEnv(replaceConfigData(config.getConfigDataEnv(), config.getId().toString(),
                rq.getAgentInfo().getDeviceNo(), authRecordDO.getPayAuthNo()));

        // 最终上下文
        PayAuthChannelContext channelContext = PayAuthChannelContext.builder()
                .authRq(rq)
                .config(config)
                .payAuthNo(authRecordDO.getPayAuthNo())
                .payMethod(rq.getPayMethod())
                .build();

        // afterCommit 发送保障消息
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        selfMqProducerV2.sendPayAuthGuaranteeMsg(channelContext.getPayAuthNo(),
                                JSON.toJSONString(PayAuthGuaranteeMsgBody.builder()
                                        .payAuthNo(channelContext.getPayAuthNo())
                                        .payConfigId(config.getId())
                                        .build()));
                    } catch (Exception e) {
                        log.error("授权保障消息发送失败. payAuthNo={}", channelContext.getPayAuthNo(), e);
                    }
                }
            });
        }

        log.info("授权准备阶段完成. bizNo={}, payAuthNo={}", rq.getBizTradeNo(), authRecordDO.getPayAuthNo());
        return new PreparedAuthContext(paymentAuthService, channelContext, authRecordDO);
    }

    private RentalAuthRecordDO buildAuthRecordDO(UnifiedPayAuthRQ rq, String payAuthNo, Long payConfigId) {
        RentalAuthRecordDO authRecordDO = new RentalAuthRecordDO();
        authRecordDO.setId(PrimKeyGenerator.generatePrimKey(TABLE_NAME_RENTAL_AUTH_RECORD, redisTemplate));
        authRecordDO.setUserId(rq.getCustomerInfo().getUserId());
        authRecordDO.setTenantId(rq.getAgentInfo().getTenantId());
        authRecordDO.setPayAuthNo(payAuthNo);
        authRecordDO.setPayAuthType(rq.getPayAuthType());
        authRecordDO.setAmount(rq.getPayAmount());
        authRecordDO.setCurrency(rq.getCurrency());
        authRecordDO.setStatus(RentalAuthStatus.INIT.getCode());
        authRecordDO.setPayType(rq.getPayWay());
        authRecordDO.setPayMethod(rq.getPayMethod());
        authRecordDO.setPayConfigId(payConfigId);
        authRecordDO.setBizAuthNo(rq.getBizTradeNo());
        authRecordDO.setCreateTime(new Date());
        authRecordDO.setGmtCreate(System.currentTimeMillis());
        authRecordDO.setGmtUpdate(System.currentTimeMillis());
        authRecordDO.setDeleted(0);
        PaymentAuthJsonExtensions paymentAuthJsonExtensions = new PaymentAuthJsonExtensions();
        paymentAuthJsonExtensions.setDeviceNo(rq.getAgentInfo().getDeviceNo());
        // 转汇处理
        if (rq.getCurrencyTransferInfo() != null) {
            CurrencyExchangeInfo currencyExchange = handleCurrencyExchange(rq);
            paymentAuthJsonExtensions.setCurrencyExchange(currencyExchange);
            // 替换请求中的货币单位和金额
            rq.setCurrency(currencyExchange.getTargetCurrency());
            rq.setPayAmount(currencyExchange.getTargetAmount().longValue());
        }

        authRecordDO.setJsonExtensions(JSON.toJSONString(paymentAuthJsonExtensions));
        return authRecordDO;
    }

    private CurrencyExchangeInfo handleCurrencyExchange(UnifiedPayAuthRQ rq) {
        CurrencyTransferInfo transferInfo = rq.getCurrencyTransferInfo();
        if (transferInfo == null) {
            return null;
        }

        log.info("开始处理汇率转换. 原始金额={}, 原始币种={}, 目标币种={}, 汇率={}",
                rq.getPayAmount(), rq.getCurrency(),
                transferInfo.getTargetCurrency(), transferInfo.getExchangeRate());

        // 计算目标金额
        MultiCurrencyMoney sourceMoney = new MultiCurrencyMoney(0, transferInfo.getCurrentCurrency());
        sourceMoney.setCent(rq.getPayAmount());

        MultiCurrencyMoney targetMoney = MultiCurrencyMoneyUtil.exchangeRate(
                sourceMoney,
                CurrencyEnum.getByCurrencyCode(transferInfo.getTargetCurrency()),
                transferInfo.getExchangeRate());

        CurrencyExchangeInfo currencyExchange = new CurrencyExchangeInfo();
        currencyExchange.setExchangeRate(transferInfo.getExchangeRate().floatValue());
        currencyExchange.setTargetAmount(Integer.valueOf(String.valueOf(targetMoney.getCent())));
        currencyExchange.setTargetCurrency(transferInfo.getTargetCurrency());
        return currencyExchange;
    }
}
