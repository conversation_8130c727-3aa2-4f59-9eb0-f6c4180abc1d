package so.dian.hera.service.v2.channel.refund.handlers;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.notify.DelayRefundV2Body;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.constant.MqDelayLevelConst;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.service.refund.RefundDelayService;
import so.dian.hera.service.v2.channel.refund.RefundStateHandler;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.common.mq.producer.v2.SelfMqProducerV2;

/**
 * 延迟退款处理器：拦截 WAITING + channelRefundDelay=true 的场景
 * 责任：发送延迟退款消息（v1 自消费渠道已有的通道），并进行幂等保护
 */
@Slf4j
@Component
@Order(100) // 优先于默认的 WAITING 处理器
public class RefundDelayHandler implements RefundStateHandler {

    @Resource
    private SelfMqProducerV2 selfMqProducerV2;
    @Resource
    private RefundDelayService refundDelayService;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RedisClient redisClient;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.WAITING;
    }

    @Override
    public boolean supportsScenario(RefundDO refundDO, UnifiedRefundRS rs) {
        return Boolean.TRUE.equals(rs.getChannelRefundDelay());
    }

    @Override
    public void handle(RefundDO refundDO, UnifiedRefundRS rs) {
        // 幂等：避免同一退款单反复发送延迟消息
        String cacheKey = refundDO.getRefundNo();
        String flag = redisClient.get(CacheEnum.MIDTRANS_SHOPPE_PAY_REFUND_DELAY_FLAG.ns, cacheKey, String.class);
        if (StringUtils.isNotEmpty(flag)) {
            log.warn("延迟退款消息已发送，跳过。refundNo={}", refundDO.getRefundNo());
            return;
        }

        // 计算延迟级别：
        // - VietQR：固定延迟 1 分钟
        // - ShopeePay（Midtrans）：默认使用 07:00 结束窗口
        int delayLevel;
        int delaySeconds;
        Integer refundType = refundDO.getRefundType();
        if (refundType != null && refundType.equals(PaywayEnum.VIETQR_STATIC.getPayway())) {
            delayLevel = MqDelayLevelConst.DELAY_1M;
            delaySeconds = 60;
        } else if (refundType != null && refundType.equals(PaywayEnum.MIDTRANS_CHECKOUT_APM.getPayway())) {
            int delayHour = 7;
            int delayMinute = 0;
            java.util.HashMap<String, Integer> delayCalc = refundDelayService.calcDelayLevel(delayHour, delayMinute);
            delayLevel = delayCalc.get("delayLevel");
            delaySeconds = delayCalc.get("delaySeconds");
        } else {
            // 其他渠道：采用保守短延迟 1 分钟
            delayLevel = MqDelayLevelConst.DELAY_1M;
            delaySeconds = 60;
        }

        // 构造延迟消息体（沿用 v1 的 DelayRefundBody，自消费入口复用 DelayRefundHandler）
        DelayRefundV2Body body = new DelayRefundV2Body();
        body.setBizTradeNo(refundDO.getOrderNo());
        body.setAmount(refundDO.getAmount().longValue());
        body.setRefundReason(refundDO.getReason());
        body.setSystem(refundDO.getSystem());
        body.setDelayLevel(delayLevel);
        body.setUserId(refundDO.getUserId());

        // 设置缓存标记，过期时间由 RefundDelayService 推导的秒数-1 保障提前触发
        redisClient.set(CacheEnum.MIDTRANS_SHOPPE_PAY_REFUND_DELAY_FLAG.ns, cacheKey, cacheKey,
                delaySeconds > 1 ? delaySeconds - 1 : delaySeconds);

        // 发送v2延迟消息
        selfMqProducerV2.sendRefundDelayInitiateV2(refundDO.getTradeNo(), JSON.toJSONString(body), delayLevel);
        log.info("[V2]延迟退款消息已发送，refundNo={}, tradeNo={}, delayLevel={}", refundDO.getRefundNo(), refundDO.getTradeNo(), delayLevel);
    }
}


