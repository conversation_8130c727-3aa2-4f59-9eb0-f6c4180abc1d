package so.dian.hera.service.v2.channel.refund.handlers;

import java.util.Date;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.client.enums.status.RentalAuthStatus;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.RentalAuthRecordDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.PaymentOrderDAO;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.hera.service.v2.channel.refund.RefundStateHandler;
import so.dian.platform.account.service.UserAccountService;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.mq.producer.v2.BizMqProducerV2;
import so.dian.platform.common.utils.ValidateUtil;

@Slf4j
@Component
@Order(100)
public class RefundConfirmSuccessHandler implements RefundStateHandler {

    @Resource
    private RefundMapper refundMapper;
    @Resource
    private PaymentOrderDAO paymentOrderMapper;
    @Resource
    private UserAccountService userAccountService;
    @Resource
    private BizMqProducerV2 bizMqProducerV2;
    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.CONFIRM_SUCCESS;
    }

    /**
     * 退款成功的处理逻辑，直接能退款成功，目前还没有。
     * 退款成功后，需要发送退款通知。
     * 如果是押金退款，还需要更新押金账户余额。
     */
    @Override
    public void handle(RefundDO refundDO, UnifiedRefundRS rs) {
        // 1.更新退款单状态
        refundDO.setStatus(RefundStatus.REFUNDED.getCode());
        refundDO.setRefundTime(new Date());
        if (StringUtils.isNotEmpty(rs.getChannelRetMsg().getChannelOrderId())) {
            refundDO.setOutTraceNo(rs.getChannelRetMsg().getChannelOrderId());
        }

        int updateResult = refundMapper.update(refundDO);
        ValidateUtil.requiredTrue(updateResult == 1, HeraBizErrorCodeEnum.ILLEGAL_ERROR);

        // 2.更新支付单退款累计与状态
        PaymentOrderDO paymentDO = paymentOrderMapper.getPaymentByPayTradeNo(refundDO.getTradeNo());
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        Long currentRefundAmount = Optional.ofNullable(paymentDO.getRefundAmount()).orElse(0L);
        Long newRefundAmount = currentRefundAmount + refundDO.getAmount();
        paymentOrderMapper.updateRefundStatus(
                PayStatus.REFUNDED.getCode(),
                new Date(),
                newRefundAmount,
                paymentDO.getPayTradeNo(),
                new Date(),
                System.currentTimeMillis());

        // 3.更新押金账户余额(如果当前是退的押金)
        if (PaymentBizTypeEnum.PAY_DEPOSIT.getCode().equals(refundDO.getBizType())) {
            // 更新押金账户余额
            userAccountService.deductDepositAccountMoney(refundDO.getUserId(), paymentDO.getTenantId(), refundDO.getAmount(), refundDO.getRefundNo());

            // 更新授权凭证状态为失效
            RentalAuthRecordDO rentalAuthRecordDO = rentalAuthRecordMapper
                    .selectByChannelAuthNo(paymentDO.getPayTradeNo());
            ValidateUtil.requiredNotNull(rentalAuthRecordDO, HeraBizErrorCodeEnum.RENTAL_AUTH_CREATE_FAIL);
            rentalAuthRecordDO.setStatus(RentalAuthStatus.INVALID.getCode());
            rentalAuthRecordDO.setExpireTime(new Date());
            rentalAuthRecordDO.setGmtUpdate(System.currentTimeMillis());
            rentalAuthRecordMapper.updateSelective(rentalAuthRecordDO);
        }

        // 4.发送退款通知
        bizMqProducerV2.sendRefundMsg(refundDO);
        log.info("RefundConfirmSuccessHandler，refundNo: {}, rs: {}", refundDO.getRefundNo(), JSON.toJSONString(rs));
    }
}
