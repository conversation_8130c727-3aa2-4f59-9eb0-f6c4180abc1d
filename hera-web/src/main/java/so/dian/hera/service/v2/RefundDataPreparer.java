package so.dian.hera.service.v2;

import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.RefundChannelFeeRecord;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.TenantPaymentConfig;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.interceptor.v2.IRefundService;
import so.dian.hera.service.refund.RefundChannelFeeRecordService;
import so.dian.hera.service.v2.enums.RefundSourceType;
import so.dian.hera.service.v2.model.PaymentRefundContext;
import so.dian.hera.service.paychannel.TenantPaymentConfigService;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.mq.producer.v2.SelfMqProducerV2;
import so.dian.platform.common.mq.producer.v2.body.RefundGuaranteeMsgBody;
import so.dian.platform.common.utils.ValidateUtil;


import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 退款数据准备器
 *
 * 负责步骤2：【有事务】准备数据并落库发保障消息
 * - 退款防重检查
 * - 创建退款单
 * - 保存退款服务费记录
 * - 发送退款保障MQ消息
 * - 退款单状态设为INIT
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefundDataPreparer {

    @Resource
    private RefundMapper refundMapper;
    @Resource
    private SelfMqProducerV2 selfMqProducerV2;
    @Resource
    private RefundChannelFeeRecordService refundChannelFeeRecordService;
    @Resource
    private BeanContextHelper beanContextHelper;
    @Resource
    private TenantPaymentConfigService tenantPaymentConfigService;

    /**
     * 准备退款数据（事务性操作）
     *
     * @param context 退款上下文
     * @return 退款单号
     */
    @Transactional(rollbackFor = Exception.class)
    public String prepareRefundData(PaymentRefundContext context) {
        log.info("开始准备退款数据，bizTradeNo: {}, refundType: {}",
                context.getOriginalRequest().getBizTradeNo(), context.getRefundType());

        // 1. 退款防重检查
        PaymentOrderDO requestPayment = context.getTargetPayment();
        RefundDO existingRefundDO = checkRefundDuplication(requestPayment, context.getRefundAmount());

        // 2. 获取实际退款支付单与渠道配置、退款服务
        PaymentOrderDO actualRefundPayment = getActualRefundPayment(context);
        TenantPaymentConfig tenantPaymentConfig = tenantPaymentConfigService
                .getRecordByIdWithCache(actualRefundPayment.getPayConfigId());
        ValidateUtil.requiredNotNull(tenantPaymentConfig, HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);
        IRefundService refundService = getRefundService(actualRefundPayment);

        // 3. 准备退款单（原单重试或新建）
        RefundDO refundDO;
        String refundNo;

        if (existingRefundDO != null) {
            // 原单重试逻辑
            refundDO = existingRefundDO;
            refundNo = existingRefundDO.getRefundNo();
            log.info("使用原退款单进行重试，refundNo: {}, status: {}", refundNo, refundDO.getStatus());

        } else {
            // 新建退款单逻辑
            refundNo = refundService.genRefundNo(actualRefundPayment, tenantPaymentConfig);
            log.info("创建新退款单，refundNo: {}", refundNo);

            refundDO = generateRefundDO(context, refundNo);

            // 4. 保存退款服务费记录（如果有）
            saveRefundFeeRecords(context, refundNo, requestPayment);
        }

        // 5. 保存/更新退款单状态为INIT
        if (existingRefundDO != null) {
            refundDO.setStatus(RefundStatus.INIT.getCode());
            refundMapper.update(refundDO);
        } else {
            refundDO.setStatus(RefundStatus.INIT.getCode());
            refundMapper.insert(refundDO);
        }

        // 6. 发送退款保障消息
        sendRefundGuaranteeMessage(refundNo, refundDO.getTradeNo(), actualRefundPayment.getPayConfigId());

        log.info("退款数据准备完成，refundNo: {}, isRetry: {}", refundNo, existingRefundDO != null);
        return refundNo;
    }

    /**
     * 根据退款类型获取实际退款的支付单
     */
    private PaymentOrderDO getActualRefundPayment(PaymentRefundContext context) {
        if (context.getRefundType() == RefundSourceType.DIRECT_PAYMENT) {
            return context.getTargetPayment();
        } else if (context.getRefundType() == RefundSourceType.DEPOSIT_DEDUCTION) {
            return context.getActualRefundPayment();
        } else if (context.getRefundType() == RefundSourceType.PRE_AUTH) {
            // 预授权场景使用目标支付单的信息
            return context.getTargetPayment();
        }
        throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);
    }

    /**
     * 生成退款单DO
     */
    private RefundDO generateRefundDO(PaymentRefundContext context, String refundNo) {
        PaymentOrderDO actualRefundPayment = getActualRefundPayment(context);

        RefundDO refundDO = new RefundDO();
        refundDO.setRefundNo(refundNo);
        refundDO.setBizType(context.getTargetPayment().getBizType());
        refundDO.setAmount(context.getRefundAmount().intValue());
        refundDO.setStatus(RefundStatus.INIT.getCode());
        refundDO.setSystem(context.getOriginalRequest().getSystem());
        refundDO.setReason(context.getOriginalRequest().getRefundReason());
        refundDO.setUserId(context.getOriginalRequest().getUserId());
        refundDO.setTradeNo(actualRefundPayment.getPayTradeNo());
        refundDO.setOrderNo(context.getOriginalRequest().getRefundTradeNo());
        refundDO.setCurrency(actualRefundPayment.getCurrency());
        refundDO.setRefundType(actualRefundPayment.getPayType());

        return refundDO;
    }

    /**
     * 保存退款服务费记录
     */
    private void saveRefundFeeRecords(PaymentRefundContext context, String refundNo, PaymentOrderDO requestPayment) {
        if (CollectionUtils.isNotEmpty(context.getOriginalRequest().getRefundFeeRequestList())) {
            List<RefundChannelFeeRecord> feeRecords = new ArrayList<>();
            for (RefundFeeRequest fee : context.getOriginalRequest().getRefundFeeRequestList()) {
                RefundChannelFeeRecord rec = new RefundChannelFeeRecord();
                rec.setPayTradeNo(requestPayment.getPayTradeNo());
                rec.setRefundNo(refundNo);
                rec.setCurrencyCode(requestPayment.getCurrency());
                rec.setAmount(fee.getAmount());
                rec.setSourceType(fee.getSourceType());
                rec.setNote(fee.getNote());
                rec.setUserId(fee.getUserId());
                rec.setAgentId(fee.getAgentId());
                feeRecords.add(rec);
            }
            try {
                refundChannelFeeRecordService.saveBatchRecord(feeRecords);
            } catch (Exception e) {
            log.error("保存退款服务费记录失败，refundNo: {}", refundNo, e);
            throw new HeraBizException(HeraBizErrorCodeEnum.DEFAULT_ERROR);
            }
        }
    }

    /**
     * 发送退款保障消息
     */
    private void sendRefundGuaranteeMessage(String refundNo, String payTradeNo, Long payConfigId) {
        try {
            RefundGuaranteeMsgBody msgBody = new RefundGuaranteeMsgBody();
            msgBody.setRefundNo(refundNo);
            msgBody.setPayTradeNo(payTradeNo);
            msgBody.setPayConfigId(payConfigId);

            selfMqProducerV2.sendRefundGuaranteeMsg(refundNo, JSON.toJSONString(msgBody));
            log.info("退款保障消息发送成功，refundNo: {}", refundNo);

        } catch (Exception e) {
            log.error("退款保障消息发送失败，refundNo: {}", refundNo, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 退款防重检查
     */
    private RefundDO checkRefundDuplication(PaymentOrderDO requestPayment, Long refundAmount) {
        log.info("开始退款防重检查，payTradeNo: {}, amount: {}", requestPayment.getPayTradeNo(), refundAmount);

        List<RefundDO> existedRefunds = refundMapper.selectByTradeNo(requestPayment.getPayTradeNo());
        RefundDO duplicateRefund = existedRefunds.stream()
                .filter(refund -> Objects.equals(refund.getAmount(), refundAmount.intValue()))
                .filter(refund -> RefundStatus.REFUNDING.getCode().equals(refund.getStatus())
                        || RefundStatus.INIT.getCode().equals(refund.getStatus()))
                .findFirst()
                .orElse(null);

        if (duplicateRefund != null) {
            log.info("发现重复退款申请，将进行原单重试，payTradeNo: {}, amount: {}, originalRefundNo: {}, status: {}",
                    requestPayment.getPayTradeNo(), refundAmount, duplicateRefund.getRefundNo(), duplicateRefund.getStatus());
        }

        return duplicateRefund;
    }

    /**
     * 获取退款服务实现
     */
    private IRefundService getRefundService(PaymentOrderDO payment) {
        // 这里简化处理，实际需要根据支付方式获取对应的服务
        // 暂时返回null，后续会在RefundChannelCaller中处理
        return beanContextHelper.getBean("defaultRefundService", IRefundService.class);
    }
}