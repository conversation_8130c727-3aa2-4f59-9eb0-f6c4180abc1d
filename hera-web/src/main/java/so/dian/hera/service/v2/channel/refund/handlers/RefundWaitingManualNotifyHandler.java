package so.dian.hera.service.v2.channel.refund.handlers;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.notify.ManualRefundNotifyDTO;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.ManualRefundNotifyTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.RefundExtensions;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.PaymentOrderDAO;
import so.dian.hera.service.v2.channel.refund.RefundStateHandler;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.common.mq.producer.BizMqProducer;

/**
 * WAITING 场景的人工退款通知处理器。
 *
 * 适用场景：
 * - IFORTEPAY 渠道无线上退款 API，需要引导商户在线下（商户后台）完成退款。
 *
 * 触发条件：
 * - 渠道返回 WAITING，且退款渠道为 IFORTEPAY。
 */
@Slf4j
@Component
@Order(90)
public class RefundWaitingManualNotifyHandler implements RefundStateHandler {

    @Resource
    private PaymentOrderDAO paymentOrderMapper;
    @Resource
    private BizMqProducer bizMqProducer;
    @Resource
    private RedisClient redisClient;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.WAITING;
    }

    @Override
    public boolean supportsScenario(RefundDO refundDO, UnifiedRefundRS rs) {
        PaywayEnum paywayEnum = PaywayEnum.explain(refundDO.getRefundType());
        return paywayEnum != null && paywayEnum.getChannel() == ChannelEnum.IFORTEPAY;
    }

    @Override
    public void handle(RefundDO refundDO, UnifiedRefundRS rs) {
        PaymentOrderDO paymentOrderDO = paymentOrderMapper.getPaymentByPayTradeNo(refundDO.getTradeNo());
        if (paymentOrderDO == null) {
            log.warn("WAITING人工退款通知#支付单不存在，跳过发送。tradeNo={}", refundDO.getTradeNo());
            return;
        }

        // 计算幂等键：refundNo + realRefundAmount + refundTime(可空)
        Integer realRefundAmount = refundDO.getAmount();
        if (StringUtils.isNotBlank(refundDO.getJsonExtensions())) {
            try {
                RefundExtensions extra = JSON.parseObject(refundDO.getJsonExtensions(), RefundExtensions.class);
                if (extra != null && extra.getRealRefundAmount() != null) {
                    realRefundAmount = extra.getRealRefundAmount().intValue();
                }
            } catch (Exception ignore) {
            }
        }
        String cacheKey = refundDO.getRefundNo() + realRefundAmount +
                (refundDO.getRefundTime() != null ? refundDO.getRefundTime().getTime() : "");
        String cacheValue = redisClient.get(CacheEnum.MANUAL_REFUND_NOTIFY_FLAG.ns, cacheKey, String.class);
        if (StringUtils.isNotEmpty(cacheValue)) {
            log.warn("WAITING人工退款通知#重复投递拦截 refundNo={}, cacheKey={}", refundDO.getRefundNo(), cacheKey);
            return;
        }

        ManualRefundNotifyDTO manualRefundNotifyDTO = ManualRefundNotifyDTO.builder()
                .userId(paymentOrderDO.getUserId())
                .refundNo(refundDO.getRefundNo())
                .refundType(ManualRefundNotifyTypeEnum.REFUND_OFFLINE.getCode())
                .refundAmount(refundDO.getAmount())
                .realRefundAmount(realRefundAmount)
                .refundFailMsg(null)
                .payTradeNo(paymentOrderDO.getPayTradeNo())
                .build();

        bizMqProducer.sendManualRefundNotifyMsg(manualRefundNotifyDTO);
        redisClient.set(CacheEnum.MANUAL_REFUND_NOTIFY_FLAG.ns, cacheKey,
                refundDO.getRefundNo(), CacheEnum.MANUAL_REFUND_NOTIFY_FLAG.expiredTime);
        log.info("WAITING人工退款通知#已发送，refundNo={}, payTradeNo={}", refundDO.getRefundNo(), paymentOrderDO.getPayTradeNo());
    }
}


