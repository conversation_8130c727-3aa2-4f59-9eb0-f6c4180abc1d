package so.dian.hera.service.v2.channel.handlers;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.base.ChannelPayRetMsg;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayRS;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RentalAuthStatus;
import com.chargebolt.hera.domain.PaymentAuthJsonExtensions;
import com.chargebolt.hera.domain.PaymentJsonExtensions;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.RentalAuthRecordDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.PaymentOrderDAO;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.hera.interceptor.v2.model.PayChannelContext;
import so.dian.hera.service.v2.channel.ChannelStateHandler;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.ValidateUtil;

import java.util.Date;

@Slf4j
@Component
public class PaymentFailHandler implements ChannelStateHandler {

    @Resource
    private PaymentOrderDAO paymentOrderMapper;
    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.CONFIRM_FAIL;
    }

    @Override
    public void handle(PaymentOrderDO paymentOrderDO, UnifiedPayRS rs, PayChannelContext context) {
        log.info("ConfirmFailHandler 支付失败，渠道单号为空 payTradeNo={}, channelNo={}", context.getPayTradeNo(),
                context.getPayChannelNo());
        ChannelPayRetMsg ret = rs.getChannelRetMsg();
        PaymentJsonExtensions extensions = JSON.parseObject(paymentOrderDO.getJsonExtensions(),
                PaymentJsonExtensions.class);
        if (extensions == null) {
            extensions = new PaymentJsonExtensions();
        }
        String errCode = StringUtils.defaultString(ret.getChannelErrCode());
        String errMsg = StringUtils.defaultString(ret.getChannelErrMsg());
        extensions.setFailReason(errCode + ":" + errMsg);
        int result = paymentOrderMapper.updateJsonExtensionsByPayTradeNo(
                context.getPayTradeNo(),
                PayStatus.INIT.getCode(),
                PayStatus.FAIL.getCode(),
                JSON.toJSONString(extensions),
                new Date(),
                System.currentTimeMillis());
        ValidateUtil.requiredTrue(result > 0, HeraBizErrorCodeEnum.PAYMENT_UPDATE_FAIL);
        if (PaymentBizTypeEnum.PAY_DEPOSIT.getCode().equals(paymentOrderDO.getBizType())) {
            RentalAuthRecordDO rentalAuthRecordDO = rentalAuthRecordMapper
                    .getRentalAuthRecordByPayAuthNo(context.getPayAuthNo());
            String jsonExtensions = rentalAuthRecordDO.getJsonExtensions();
            PaymentAuthJsonExtensions paymentAuthJsonExtensions = JSON.parseObject(jsonExtensions,
                    PaymentAuthJsonExtensions.class);
            paymentAuthJsonExtensions.setFailReason(errCode + ":" + errMsg);
            rentalAuthRecordDO.setJsonExtensions(JSON.toJSONString(paymentAuthJsonExtensions));
            int updateResult = rentalAuthRecordMapper.updateJsonExtensionsByPayAuthNo(
                    context.getPayAuthNo(),
                    RentalAuthStatus.INIT.getCode(),
                    RentalAuthStatus.AUTH_FAILED.getCode(),
                    JSON.toJSONString(paymentAuthJsonExtensions),
                    System.currentTimeMillis());
            ValidateUtil.requiredTrue(updateResult > 0, HeraBizErrorCodeEnum.RENTAL_AUTH_UPDATE_FAIL);
        }
        log.info("支付失败，已更新失败原因 payTradeNo={}, errCode={}, errMsg={}", context.getPayTradeNo(), errCode, errMsg);
    }
}
