package so.dian.hera.service.v2.channel.auth.handlers;

import javax.annotation.Resource;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;
import com.chargebolt.hera.domain.RentalAuthRecordDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.hera.service.v2.channel.auth.AuthStateHandler;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

@Slf4j
@Component
@Order(100)
public class AuthConfirmSuccessHandler implements AuthStateHandler {

    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.CONFIRM_SUCCESS;
    }

    @Override
    public boolean supportsScenario(RentalAuthRecordDO record, UnifiedPayAuthRS rs) {
        // 可按授权类型过滤场景，例如仅处理押金预授权
        return true;
    }

    @Override
    public void handle(RentalAuthRecordDO record, UnifiedPayAuthRS rs) {
        // 与现有实现保持一致：同步成功先不直接置终态，仅回写渠道授权单号（如果存在）
        log.error("授权同步成功，异常场景，payAuthNO={},result={}", record.getPayAuthNo(), JSON.toJSONString(rs));
        throw HeraBizException.create(HeraBizErrorCodeEnum.DEFAULT_ERROR);
    }
}
