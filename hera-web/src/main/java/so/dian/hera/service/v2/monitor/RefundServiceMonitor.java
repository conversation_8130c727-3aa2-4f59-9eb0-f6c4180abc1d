package so.dian.hera.service.v2.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.hera.config.RefundServiceConfig;
import so.dian.hera.service.v2.enums.RefundSourceType;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 退款服务监控组件
 *
 * 负责收集和记录退款服务的详细执行指标
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefundServiceMonitor {

    @Resource
    private RefundServiceConfig refundServiceConfig;

    // 统计指标
    private final AtomicLong simplifiedServiceTotal = new AtomicLong(0);
    private final AtomicLong simplifiedServiceSuccess = new AtomicLong(0);
    private final AtomicLong simplifiedServiceFailure = new AtomicLong(0);
    private final AtomicLong simplifiedServiceFallback = new AtomicLong(0);

    private final AtomicLong originalServiceTotal = new AtomicLong(0);
    private final AtomicLong originalServiceSuccess = new AtomicLong(0);
    private final AtomicLong originalServiceFailure = new AtomicLong(0);

    // 按退款类型统计
    private final ConcurrentHashMap<RefundSourceType, AtomicLong> refundTypeStats = new ConcurrentHashMap<>();

    // 性能指标
    private final ConcurrentHashMap<String, Long> stepExecutionTimes = new ConcurrentHashMap<>();

    /**
     * 记录简化版服务执行开始
     */
    public void recordSimplifiedServiceStart(String bizTradeNo, RefundSourceType refundType) {
        if (!refundServiceConfig.isEnableDetailedLogging()) {
            return;
        }

        simplifiedServiceTotal.incrementAndGet();
        refundTypeStats.computeIfAbsent(refundType, k -> new AtomicLong(0)).incrementAndGet();

        log.info("退款监控 - 简化版服务开始执行，bizTradeNo: {}, refundType: {}, 总计: {}",
                bizTradeNo, refundType, simplifiedServiceTotal.get());
    }

    /**
     * 记录简化版服务执行成功
     */
    public void recordSimplifiedServiceSuccess(String bizTradeNo, long executionTimeMs) {
        if (!refundServiceConfig.isEnableDetailedLogging()) {
            return;
        }

        simplifiedServiceSuccess.incrementAndGet();

        log.info("退款监控 - 简化版服务执行成功，bizTradeNo: {}, 耗时: {}ms, 成功率: {:.2f}%",
                bizTradeNo, executionTimeMs, getSimplifiedServiceSuccessRate());

        // 记录性能指标
        if (refundServiceConfig.isEnablePerformanceMonitoring()) {
            recordPerformanceMetric("simplified_service_execution", executionTimeMs);
        }
    }

    /**
     * 记录简化版服务执行失败
     */
    public void recordSimplifiedServiceFailure(String bizTradeNo, String errorMessage, boolean fallback) {
        if (!refundServiceConfig.isEnableDetailedLogging()) {
            return;
        }

        simplifiedServiceFailure.incrementAndGet();
        if (fallback) {
            simplifiedServiceFallback.incrementAndGet();
        }

        log.error("退款监控 - 简化版服务执行失败，bizTradeNo: {}, error: {}, fallback: {}, 成功率: {:.2f}%",
                bizTradeNo, errorMessage, fallback, getSimplifiedServiceSuccessRate());
    }

    /**
     * 记录原有服务执行开始
     */
    public void recordOriginalServiceStart(String bizTradeNo, RefundSourceType refundType) {
        if (!refundServiceConfig.isEnableDetailedLogging()) {
            return;
        }

        originalServiceTotal.incrementAndGet();

        log.info("退款监控 - 原有服务开始执行，bizTradeNo: {}, refundType: {}, 总计: {}",
                bizTradeNo, refundType, originalServiceTotal.get());
    }

    /**
     * 记录原有服务执行成功
     */
    public void recordOriginalServiceSuccess(String bizTradeNo, long executionTimeMs) {
        if (!refundServiceConfig.isEnableDetailedLogging()) {
            return;
        }

        originalServiceSuccess.incrementAndGet();

        log.info("退款监控 - 原有服务执行成功，bizTradeNo: {}, 耗时: {}ms, 成功率: {:.2f}%",
                bizTradeNo, executionTimeMs, getOriginalServiceSuccessRate());

        // 记录性能指标
        if (refundServiceConfig.isEnablePerformanceMonitoring()) {
            recordPerformanceMetric("original_service_execution", executionTimeMs);
        }
    }

    /**
     * 记录原有服务执行失败
     */
    public void recordOriginalServiceFailure(String bizTradeNo, String errorMessage) {
        if (!refundServiceConfig.isEnableDetailedLogging()) {
            return;
        }

        originalServiceFailure.incrementAndGet();

        log.error("退款监控 - 原有服务执行失败，bizTradeNo: {}, error: {}, 成功率: {:.2f}%",
                bizTradeNo, errorMessage, getOriginalServiceSuccessRate());
    }

    /**
     * 记录步骤执行时间
     */
    public void recordStepExecutionTime(String stepName, String bizTradeNo, long executionTimeMs) {
        if (!refundServiceConfig.isEnablePerformanceMonitoring()) {
            return;
        }

        String key = stepName + "_" + System.currentTimeMillis() / 60000; // 按分钟聚合
        stepExecutionTimes.put(key, executionTimeMs);

        log.info("退款监控 - 步骤执行时间，step: {}, bizTradeNo: {}, 耗时: {}ms",
                stepName, bizTradeNo, executionTimeMs);

        // 检查是否超过阈值
        if (executionTimeMs > refundServiceConfig.getStepTimeoutMs()) {
            log.warn("退款监控 - 步骤执行超时告警，step: {}, bizTradeNo: {}, 耗时: {}ms, 阈值: {}ms",
                    stepName, bizTradeNo, executionTimeMs, refundServiceConfig.getStepTimeoutMs());
        }
    }

    /**
     * 记录性能指标
     */
    private void recordPerformanceMetric(String metricName, long value) {
        // 这里可以集成到具体的监控系统，如Prometheus、InfluxDB等
        log.info("性能指标 - {}: {}ms", metricName, value);
    }

    /**
     * 获取简化版服务成功率
     */
    private double getSimplifiedServiceSuccessRate() {
        long total = simplifiedServiceTotal.get();
        if (total == 0) return 0.0;
        return (double) simplifiedServiceSuccess.get() / total * 100;
    }

    /**
     * 获取原有服务成功率
     */
    private double getOriginalServiceSuccessRate() {
        long total = originalServiceTotal.get();
        if (total == 0) return 0.0;
        return (double) originalServiceSuccess.get() / total * 100;
    }

    /**
     * 打印统计报告
     */
    public void printStatisticsReport() {
        if (!refundServiceConfig.isEnableDetailedLogging()) {
            return;
        }

        log.info("=== 退款服务统计报告 ===");
        log.info("简化版服务 - 总计: {}, 成功: {}, 失败: {}, 回退: {}, 成功率: {:.2f}%",
                simplifiedServiceTotal.get(), simplifiedServiceSuccess.get(),
                simplifiedServiceFailure.get(), simplifiedServiceFallback.get(),
                getSimplifiedServiceSuccessRate());

        log.info("原有服务 - 总计: {}, 成功: {}, 失败: {}, 成功率: {:.2f}%",
                originalServiceTotal.get(), originalServiceSuccess.get(),
                originalServiceFailure.get(), getOriginalServiceSuccessRate());

        log.info("按退款类型统计:");
        refundTypeStats.forEach((type, count) ->
            log.info("  {}: {} 次", type, count.get()));

        log.info("========================");
    }

    /**
     * 重置统计数据
     */
    public void resetStatistics() {
        simplifiedServiceTotal.set(0);
        simplifiedServiceSuccess.set(0);
        simplifiedServiceFailure.set(0);
        simplifiedServiceFallback.set(0);

        originalServiceTotal.set(0);
        originalServiceSuccess.set(0);
        originalServiceFailure.set(0);

        refundTypeStats.clear();
        stepExecutionTimes.clear();

        log.info("退款监控 - 统计数据已重置");
    }
}