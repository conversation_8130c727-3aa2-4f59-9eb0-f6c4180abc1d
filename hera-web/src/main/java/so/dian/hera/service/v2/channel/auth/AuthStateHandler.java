package so.dian.hera.service.v2.channel.auth;

import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;
import com.chargebolt.hera.domain.RentalAuthRecordDO;

/**
 * 授权渠道返回状态处理器
 */
public interface AuthStateHandler {

    ChannelRetMsg.ChannelState getSupportedState();

    default boolean supportsScenario(RentalAuthRecordDO record, UnifiedPayAuthRS rs) {
        return true;
    }

    void handle(RentalAuthRecordDO record, UnifiedPayAuthRS rs);
}
