package so.dian.hera.service.v2;

import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.client.dto.pay.refund.v2.RefundRQ;
import com.chargebolt.hera.client.enums.PayAuthTypeEnum;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.domain.PaymentAuthJsonExtensions;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.RentalAuthRecordDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.PaymentOrderDAO;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.hera.service.v2.enums.RefundSourceType;
import so.dian.hera.service.v2.model.PaymentRefundContext;
import so.dian.hera.service.v2.monitor.RefundServiceMonitor;
import so.dian.mofa3.lang.domain.Result;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.utils.ValidateUtil;

/**
 * 简化版退款服务
 *
 * 采用四步骤架构：
 * 1. 【无事务】退款对应支付单溯源
 * 2. 【有事务】准备数据并落库发保障消息
 * 3. 【无事务】调用渠道进行退款
 * 4. 【有事务】处理退款结果
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SimplifiedRefundService {

    @Resource
    private PaymentOrderDAO paymentOrderMapper;
    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;
    @Resource
    private RefundDataPreparer refundDataPreparer;
    @Resource
    private RefundChannelCaller refundChannelCaller;
    @Resource
    private RefundResultProcessor refundResultProcessor;
    @Resource
    private RefundServiceMonitor refundServiceMonitor;
    @Resource
    private RefundExecutor refundExecutor;

    /**
     * 简化版统一退款入口
     *
     * @param refundRQ 退款请求
     * @return 退款结果
     */
    public Result<UnifiedRefundRS> refund(RefundRQ refundRQ) {
        log.info("开始处理简化版统一退款请求，bizTradeNo: {}, amount: {}", refundRQ.getBizTradeNo(), refundRQ.getAmount());

        PaymentRefundContext refundContext = null;

        // 步骤1: 【无事务】退款对应支付单溯源
        refundContext = traceRefundSource(refundRQ);

        // 补充：直接支付场景可退金额校验，保持与旧实现一致
        if (refundContext.getRefundType() == RefundSourceType.DIRECT_PAYMENT) {
            refundExecutor.validateRefundableAmount(refundRQ, refundContext.getTargetPayment());
        }

        // 步骤2: 【有事务】准备数据并落库发保障消息
        String refundNo = refundDataPreparer.prepareRefundData(refundContext);

        // 步骤3: 【无事务】调用渠道进行退款
        UnifiedRefundRS channelResult = refundChannelCaller.callRefundChannel(refundContext, refundNo);

        // 步骤4: 【有事务】处理退款结果
        refundResultProcessor.processRefundResult(refundNo, channelResult);

        return Result.success(channelResult);

    }

    /**
     * 步骤1: 退款对应支付单溯源（无事务）
     * 整合原RefundTraceService的溯源逻辑
     */
    private PaymentRefundContext traceRefundSource(RefundRQ refundRQ) {
        log.info("开始退款溯源，bizTradeNo: {}", refundRQ.getBizTradeNo());

        // 1. 查找目标支付单
        PaymentOrderDO targetPayment = paymentOrderMapper.getPaymentByBizTradeNo(refundRQ.getBizTradeNo());
        ValidateUtil.requiredNotNull(targetPayment, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        // 2. 构建退款上下文
        PaymentRefundContext context = new PaymentRefundContext();
        context.setTargetPayment(targetPayment);
        context.setRefundAmount(refundRQ.getAmount());
        context.setOriginalRequest(refundRQ);

        // 3. 根据支付类型进行溯源
        PaymentBizTypeEnum bizType = PaymentBizTypeEnum.explain(targetPayment.getBizType());
        ValidateUtil.requiredNotNull(bizType, HeraBizErrorCodeEnum.PAY_BIZ_TYPE_NOT_EXIST);

        if (PaymentBizTypeEnum.PAY_DEPOSIT == bizType) {
            // 押金充值：直接对当前支付单退款
            context.setRefundType(RefundSourceType.DIRECT_PAYMENT);
            context.setActualRefundPayment(context.getTargetPayment());
            log.info("溯源结果：押金充值直接退款，payTradeNo: {}", targetPayment.getPayTradeNo());

        } else if (PaymentBizTypeEnum.PAY_ORDER == bizType) {
            // 订单支付：需要继续溯源
            traceOrderPaymentSource(context);

        } else {
            log.error("不支持的支付业务类型: {}", bizType);
            throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);
        }

        return context;
    }

    /**
     * 订单支付溯源（核心逻辑）
     * 基于订单号找到授权凭证，进而找到押金充值支付单
     */
    private void traceOrderPaymentSource(PaymentRefundContext context) {
        log.info("开始订单支付溯源，payAuthNo: {}", context.getTargetPayment().getPayAuthNo());

        // 查找预授权记录
        RentalAuthRecordDO authRecord = rentalAuthRecordMapper
                .getRentalAuthRecordByPayAuthNo(context.getTargetPayment().getPayAuthNo());
        ValidateUtil.requiredNotNull(authRecord, HeraBizErrorCodeEnum.PARAM_ERROR);
        log.info("找到授权凭证记录，payAuthNo: {}, amount: {}", authRecord.getPayAuthNo(), authRecord.getAmount());

        // 查找预授权记录
        PayAuthTypeEnum authType = PayAuthTypeEnum.fromCode(authRecord.getPayAuthType());
        ValidateUtil.requiredNotNull(authType, HeraBizErrorCodeEnum.PARAM_ERROR);

        switch (authType) {
            case CARD_PRE_AUTH:
                // 预授权请款：使用授权凭证的第三方单号
                tracePreAuthCaptureSource(context, authRecord);
                break;

            case DEPOSIT:
                // 押金抵扣：判断是手动支付还是押金抵扣
                traceDepositSource(context, authRecord);
                break;

            default:
                log.error("不支持的授权类型: {}", authType);
                throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);
        }
    }

    /**
     * 押金来源溯源
     */
    private void traceDepositSource(PaymentRefundContext context, RentalAuthRecordDO authRecord) {
        PaymentAuthJsonExtensions jsonExtensions = JSON.parseObject(authRecord.getJsonExtensions(),
                PaymentAuthJsonExtensions.class);

        // 单独支付订单场景：目标支付单的三方单号与授权凭证的扩展字段中的押金支付三方单号不一致
        if (!Objects.equals(jsonExtensions.getDepositPayChannelNo(), context.getTargetPayment().getPayChannelNo())) {
            context.setRefundType(RefundSourceType.DIRECT_PAYMENT);
            context.setActualRefundPayment(context.getTargetPayment());
            context.setActualRefundChannelNo(context.getTargetPayment().getPayChannelNo());
            log.info("溯源结果：手动支付订单直接退款，payTradeNo: {}", context.getTargetPayment().getPayTradeNo());

        } else {
            // 押金抵扣场景：找到押金充值支付单
            traceDepositDeductionSource(context, authRecord);
        }
    }

    /**
     * 押金抵扣溯源
     * 通过授权凭证找到押金充值支付单
     */
    private void traceDepositDeductionSource(PaymentRefundContext context, RentalAuthRecordDO authRecord) {
        // 从授权凭证的扩展字段中获取押金充值交易号
        String depositPayTradeNo = authRecord.getChannelAuthNo();
        if (StringUtils.isEmpty(depositPayTradeNo)) {
            log.error("押金抵扣溯源失败：授权凭证中缺少押金充值交易号");
            throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);
        }

        // 查找押金充值支付单
        PaymentOrderDO depositPayment = paymentOrderMapper.getPaymentByPayTradeNo(depositPayTradeNo);
        ValidateUtil.requiredNotNull(depositPayment, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        log.info("押金抵扣溯源成功，depositPayTradeNo: {}, amount: {}", depositPayment.getPayTradeNo(),
                depositPayment.getPayAmount());

        context.setRefundType(RefundSourceType.DEPOSIT_DEDUCTION);
        context.setActualRefundPayment(depositPayment);
        context.setAuthRecord(authRecord);
    }

    /**
     * 预授权请款溯源
     */
    private void tracePreAuthCaptureSource(PaymentRefundContext context, RentalAuthRecordDO authRecord) {
        log.info("开始预授权请款溯源，authNo: {}, channelAuthNo: {}", authRecord.getPayAuthNo(), authRecord.getChannelAuthNo());

        // 使用授权凭证中的第三方授权号进行退款
        context.setRefundType(RefundSourceType.PRE_AUTH);
        // 实际三方支付单号从授权记录中提取
        context.setActualRefundChannelNo(authRecord.getChannelAuthNo());
        context.setAuthRecord(authRecord);

        log.info("预授权请款溯源成功，将使用第三方授权号进行退款: {}", authRecord.getChannelAuthNo());
    }
}