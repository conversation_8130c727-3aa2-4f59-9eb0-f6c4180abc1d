package so.dian.hera.service.v2.channel.auth.handlers;

import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import so.dian.hera.dao.rds.hera.RentalAuthRecordMapper;
import so.dian.hera.service.v2.channel.auth.AuthStateHandler;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.ValidateUtil;

import javax.annotation.Resource;

@Slf4j
@Component
@Order(1000)
public class AuthWaitingHandler implements AuthStateHandler {

    @Resource
    private RentalAuthRecordMapper rentalAuthRecordMapper;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.WAITING;
    }

    @Override
    public void handle(RentalAuthRecordDO record, UnifiedPayAuthRS rs) {
        String channelAuthNo = rs.getChannelRetMsg().getChannelOrderId();
        if (StringUtils.isNotBlank(channelAuthNo)) {
            int r = rentalAuthRecordMapper.updateChannelAuthNoByPayAuthNo(record.getPayAuthNo(), channelAuthNo, System.currentTimeMillis());
            ValidateUtil.requiredTrue(r > 0, HeraBizErrorCodeEnum.RENTAL_AUTH_UPDATE_FAIL);
        }
        log.info("授权处理中状态处理完成，rentalAuthNo={}", record.getPayAuthNo());
    }
}
