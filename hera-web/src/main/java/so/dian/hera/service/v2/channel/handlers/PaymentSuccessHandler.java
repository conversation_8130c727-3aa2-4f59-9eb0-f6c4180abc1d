package so.dian.hera.service.v2.channel.handlers;

import java.util.Date;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.chargebolt.hera.client.dto.param.base.ChannelPayRetMsg;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayRS;
import com.chargebolt.hera.client.enums.NotifyTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.notify.CallbackNotifyBody;
import com.chargebolt.hera.domain.notify.v2.ChannelNotifyBodyV2;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.interceptor.v2.model.PayChannelContext;
import so.dian.hera.service.v2.business.PaymentSuccessProcessor;
import so.dian.hera.service.v2.channel.ChannelStateHandler;

@Slf4j
@Component
public class PaymentSuccessHandler implements ChannelStateHandler {

    @Resource
    private PaymentSuccessProcessor paymentSuccessProcessor;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.CONFIRM_SUCCESS;
    }

    @Override
    public void handle(PaymentOrderDO paymentOrderDO, UnifiedPayRS rs, PayChannelContext context) {
        log.info("ConfirmSuccessHandler 支付成功，渠道单号为空 payTradeNo={}, channelNo={}", context.getPayTradeNo(),
                context.getPayChannelNo());
        // 这里明确只有押金抵扣和 VietQR 场景才存在直接返回确认成功的情况
        if (!PaywayEnum.DEPOSIT.getPayway().equals(paymentOrderDO.getPayType())
                && !PaywayEnum.VIETQR_STATIC.getPayway().equals(paymentOrderDO.getPayType())) {
            log.error("支付成功，不支持的支付方式 payTradeNo={}, payType={}", context.getPayTradeNo(), paymentOrderDO.getPayType());
            return;
        }

        ChannelPayRetMsg ret =  rs.getChannelRetMsg();
        String channelNo = StringUtils.isNotBlank(ret.getChannelOrderId()) ? ret.getChannelOrderId()
                : rs.getPayChannelNo();

        // 统一构造成功通知，交由业务处理器完成落库与业务通知
        ChannelNotifyBodyV2 notifyBodyV2 = new ChannelNotifyBodyV2();
        notifyBodyV2.setPayWay(PaywayEnum.explain(paymentOrderDO.getPayType()));
        notifyBodyV2.setPayTradeNo(paymentOrderDO.getPayTradeNo());
        notifyBodyV2.setPayChannelNo(channelNo);
        notifyBodyV2.setStatus(PayStatus.PAID);
        notifyBodyV2.setPayTime(new Date());
        notifyBodyV2.setDate(new Date());

        paymentSuccessProcessor.process(
                notifyBodyV2,
                CallbackNotifyBody.builder()
                        .channelId(context.getConfig().getChannelId())
                        .configId(context.getConfig().getId())
                        .notifyBody(null)
                        .notifyType(NotifyTypeEnum.QUERY.getCode())
                        .build());

        log.info("支付同步成功，已触发业务处理 payTradeNo={}, channelNo={}", context.getPayTradeNo(), channelNo);
    }
}
