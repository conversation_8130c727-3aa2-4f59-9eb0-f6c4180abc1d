package so.dian.hera.service.v2;

import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.domain.RefundDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.service.v2.channel.refund.RefundChannelResponseProcessor;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.utils.ValidateUtil;

import javax.annotation.Resource;

/**
 * 退款结果处理器
 *
 * 负责步骤4：【有事务】处理退款结果
 * - 根据渠道返回结果更新退款单状态
 * - 处理退款成功/失败的后续逻辑
 * - 记录退款流水
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefundResultProcessor {

    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RefundChannelResponseProcessor refundChannelResponseProcessor;

    /**
     * 处理退款结果（事务性操作）
     *
     * @param refundNo      退款单号
     * @param channelResult 渠道返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void processRefundResult(String refundNo, UnifiedRefundRS channelResult) {
        log.info("开始处理退款结果，refundNo: {}, channelResult: {}", refundNo,
                channelResult != null ? channelResult.getRefundNo() : "null");

        try {
            // 1. 获取退款单
            RefundDO refundDO = refundMapper.select(refundNo);
            ValidateUtil.requiredNotNull(refundDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

            // 2. 校验渠道结果
            if (channelResult == null) {
                log.error("渠道返回结果为空，refundNo: {}", refundNo);
                throw new HeraBizException(HeraBizErrorCodeEnum.DEFAULT_ERROR.getCode(), "渠道返回结果为空");
            }

            // 3. 使用现有的渠道响应处理器处理结果
            // 这个处理器包含了状态更新、流水记录等完整逻辑
            refundChannelResponseProcessor.process(refundDO, channelResult);

            log.info("退款结果处理完成，refundNo: {}, finalStatus: {}", refundNo, refundDO.getStatus());

        } catch (Exception e) {
            log.error("退款结果处理失败，refundNo: {}, error: {}", refundNo, e.getMessage(), e);

            // 处理异常情况：将退款单状态标记为失败
            try {
                handleProcessingFailure(refundNo, e);
            } catch (Exception ex) {
                log.error("处理退款结果异常时发生错误，refundNo: {}", refundNo, ex);
            }

            throw e;
        }
    }

    /**
     * 处理退款结果处理失败的情况
     *
     * @param refundNo 退款单号
     * @param originalException 原始异常
     */
    private void handleProcessingFailure(String refundNo, Exception originalException) {
        log.info("开始处理退款结果处理失败的情况，refundNo: {}", refundNo);

        try {
            RefundDO refundDO = refundMapper.select(refundNo);
            if (refundDO != null) {
                // 根据异常类型决定如何处理
                if (originalException instanceof HeraBizException) {
                    HeraBizException bizException = (HeraBizException) originalException;
                    // 业务异常：可能需要重试或特殊处理
                    log.warn("退款结果处理业务异常，refundNo: {}, code: {}, message: {}",
                            refundNo, bizException.getCode(), bizException.getMsg());

                    // 这里可以根据具体的业务错误码决定是否标记为失败
                    // 暂时不修改状态，依赖保障消息重试

                } else {
                    // 系统异常：标记为失败状态
                    log.error("退款结果处理系统异常，将退款单标记为失败，refundNo: {}", refundNo);

                    // 注意：这里要小心，避免覆盖可能已经成功的状态
                    // 只有在退款中状态时才标记为失败
                    if (refundDO.getStatus() != null && refundDO.getStatus().intValue() == com.chargebolt.hera.client.enums.status.RefundStatus.REFUNDING.getCode()) {
                        refundDO.setStatus(com.chargebolt.hera.client.enums.status.RefundStatus.FAIL.getCode());
                        refundDO.setErrorMsg("退款结果处理系统异常: " + originalException.getMessage());
                        refundMapper.update(refundDO);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理退款结果处理失败时发生异常，refundNo: {}", refundNo, e);
        }
    }

    /**
     * 查询退款单当前状态
     *
     * @param refundNo 退款单号
     * @return 退款单信息
     */
    public RefundDO getRefundStatus(String refundNo) {
        log.info("查询退款单状态，refundNo: {}", refundNo);

        RefundDO refundDO = refundMapper.select(refundNo);
        ValidateUtil.requiredNotNull(refundDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        log.info("退款单状态查询完成，refundNo: {}, status: {}", refundNo, refundDO.getStatus());
        return refundDO;
    }

    /**
     * 重试退款结果处理（供保障消息使用）
     *
     * @param refundNo      退款单号
     * @param channelResult 渠道返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void retryProcessRefundResult(String refundNo, UnifiedRefundRS channelResult) {
        log.info("重试处理退款结果，refundNo: {}", refundNo);

        RefundDO refundDO = refundMapper.select(refundNo);
        ValidateUtil.requiredNotNull(refundDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        // 检查退款单状态，避免重复处理
        if (refundDO.getStatus() != null && (refundDO.getStatus().intValue() == com.chargebolt.hera.client.enums.status.RefundStatus.REFUNDED.getCode()
                || refundDO.getStatus().intValue() == com.chargebolt.hera.client.enums.status.RefundStatus.FAIL.getCode())) {
            log.info("退款单已是最终状态，无需重复处理，refundNo: {}, status: {}", refundNo, refundDO.getStatus());
            return;
        }

        // 重新处理结果
        processRefundResult(refundNo, channelResult);
    }
}