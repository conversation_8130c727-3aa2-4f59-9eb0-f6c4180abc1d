package so.dian.hera.service.v2.channel.handlers;

import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.chargebolt.hera.client.dto.param.base.ChannelPayRetMsg;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayRS;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.PaymentOrderDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.PaymentOrderDAO;
import so.dian.hera.interceptor.v2.model.PayChannelContext;
import so.dian.hera.service.v2.channel.ChannelStateHandler;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.ValidateUtil;

@Slf4j
@Component
public class PaymentWaitingHandler implements ChannelStateHandler {

    @Resource
    private PaymentOrderDAO paymentOrderMapper;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.WAITING;
    }

    @Override
    public void handle(PaymentOrderDO paymentOrderDO, UnifiedPayRS rs, PayChannelContext context) {
        log.info("WaitingHandler 支付处理中，渠道单号为空 payTradeNo={}, channelNo={}", context.getPayTradeNo(),
                context.getPayChannelNo());
        ChannelPayRetMsg ret = rs.getChannelRetMsg();
        String channelNo = StringUtils.isNotBlank(ret.getChannelOrderId()) ? ret.getChannelOrderId()
                : rs.getPayChannelNo();
        if (StringUtils.isBlank(channelNo)) {
            log.info("支付处理中，渠道单号为空 payTradeNo={}, channelNo={}", context.getPayTradeNo(), channelNo);
            return;
        }
        // 只在 INIT 状态下更新渠道单号，避免已终态被回写
        int result = paymentOrderMapper.updatePayChannelNoIfStatusByPayTradeNo(
                context.getPayTradeNo(),
                PayStatus.INIT.getCode(),
                channelNo,
                new Date(),
                System.currentTimeMillis());
        ValidateUtil.requiredTrue(result > 0, HeraBizErrorCodeEnum.PAYMENT_UPDATE_FAIL);
        
        log.info("支付处理中，已保存渠道单号 payTradeNo={}, channelNo={}", context.getPayTradeNo(), channelNo);
    }
}
