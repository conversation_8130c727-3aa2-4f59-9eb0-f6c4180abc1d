package so.dian.hera.service.v2.channel.auth;

import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.stereotype.Component;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.utils.ValidateUtil;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class AuthChannelResponseProcessor {

    @Resource
    private List<AuthStateHandler> authStateHandlers;

    private final Map<ChannelRetMsg.ChannelState, List<AuthStateHandler>> handlerMap = new EnumMap<>(
            ChannelRetMsg.ChannelState.class);

    @PostConstruct
    public void init() {
        if (authStateHandlers != null) {
            for (AuthStateHandler handler : authStateHandlers) {
                handlerMap.computeIfAbsent(handler.getSupportedState(), k -> new ArrayList<>()).add(handler);
            }
            handlerMap.values().forEach(list -> list.sort(AnnotationAwareOrderComparator.INSTANCE));
        }
    }

    public void process(RentalAuthRecordDO record, UnifiedPayAuthRS rs) {
        ChannelRetMsg ret = rs.getChannelRetMsg();
        ValidateUtil.requiredNotNull(ret, HeraBizErrorCodeEnum.CHANNEL_STATE_NOT_SUPPORT);
        ValidateUtil.requiredNotNull(ret.getChannelState(), HeraBizErrorCodeEnum.CHANNEL_STATE_NOT_SUPPORT);

        List<AuthStateHandler> handlers = handlerMap.getOrDefault(ret.getChannelState(), Collections.emptyList());
        for (AuthStateHandler handler : handlers) {
            if (handler.supportsScenario(record, rs)) {
                handler.handle(record, rs);
                return;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.CHANNEL_STATE_NOT_SUPPORT);
    }
}
