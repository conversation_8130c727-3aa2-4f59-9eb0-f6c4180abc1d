package so.dian.hera.service.v2.channel.refund.handlers;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.chargebolt.hera.client.dto.notify.ManualRefundNotifyDTO;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.client.enums.ManualRefundNotifyTypeEnum;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.PaymentOrderDO;
import com.chargebolt.hera.domain.RefundDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.PaymentOrderDAO;
import so.dian.hera.service.v2.channel.refund.RefundStateHandler;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.common.mq.producer.BizMqProducer;

/**
 * CONFIRM_FAIL 场景的人工退款通知处理器。
 *
 * 适用场景：
 * - Midtrans ShopeePay 在限制时间段或者策略导致的申请失败，需要通知人工处理。
 *
 * 触发条件（示例规则，可继续扩展）：
 * - 渠道为 MIDTRANS_CHECKOUT_APM 且支付方式为 ShopeePay。
 */
@Slf4j
@Component
@Order(90)
public class RefundConfirmFailManualNotifyHandler implements RefundStateHandler {

    @Resource
    private PaymentOrderDAO paymentOrderMapper;
    @Resource
    private BizMqProducer bizMqProducer;
    @Resource
    private RedisClient redisClient;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.CONFIRM_FAIL;
    }

    @Override
    public boolean supportsScenario(RefundDO refundDO, UnifiedRefundRS rs) {
        // 仅针对 Midtrans APM 且 payMethod = ShopeePay 的失败场景，触发人工通知
        PaywayEnum paywayEnum = PaywayEnum.explain(refundDO.getRefundType());
        if (paywayEnum == null || paywayEnum != PaywayEnum.MIDTRANS_CHECKOUT_APM) {
            return false;
        }
        PaymentOrderDO paymentOrderDO = paymentOrderMapper.getPaymentByPayTradeNo(refundDO.getTradeNo());
        return paymentOrderDO != null && paymentOrderDO.getPayMethod() != null
                && paymentOrderDO.getPayMethod().equals(PayMethodEnum.ShopeePay.getId());
    }

    @Override
    public void handle(RefundDO refundDO, UnifiedRefundRS rs) {
        PaymentOrderDO paymentOrderDO = paymentOrderMapper.getPaymentByPayTradeNo(refundDO.getTradeNo());
        if (paymentOrderDO == null) {
            log.warn("FAIL人工退款通知#支付单不存在，跳过发送。tradeNo={}", refundDO.getTradeNo());
            return;
        }

        // 计算幂等键：refundNo + amount + refundTime(可空)
        String cacheKey = refundDO.getRefundNo() + refundDO.getAmount() +
                (refundDO.getRefundTime() != null ? refundDO.getRefundTime().getTime() : "");
        String cacheValue = redisClient.get(CacheEnum.MANUAL_REFUND_NOTIFY_FLAG.ns, cacheKey, String.class);
        if (StringUtils.isNotEmpty(cacheValue)) {
            log.warn("FAIL人工退款通知#重复投递拦截 refundNo={}, cacheKey={}", refundDO.getRefundNo(), cacheKey);
            return;
        }

        ManualRefundNotifyDTO manualRefundNotifyDTO = ManualRefundNotifyDTO.builder()
                .userId(paymentOrderDO.getUserId())
                .refundNo(refundDO.getRefundNo())
                .refundType(ManualRefundNotifyTypeEnum.REFUND_APPLY_FAIL.getCode())
                .refundAmount(refundDO.getAmount())
                .realRefundAmount(refundDO.getAmount())
                .refundFailMsg(StringUtils.defaultIfBlank(
                        rs.getChannelRetMsg() != null ? rs.getChannelRetMsg().getChannelErrMsg() : null,
                        "refund apply failed"))
                .payTradeNo(paymentOrderDO.getPayTradeNo())
                .build();

        bizMqProducer.sendManualRefundNotifyMsg(manualRefundNotifyDTO);
        redisClient.set(CacheEnum.MANUAL_REFUND_NOTIFY_FLAG.ns, cacheKey,
                refundDO.getRefundNo(), CacheEnum.MANUAL_REFUND_NOTIFY_FLAG.expiredTime);
        log.info("FAIL人工退款通知#已发送，refundNo={}, payTradeNo={}", refundDO.getRefundNo(), paymentOrderDO.getPayTradeNo());
    }
}


