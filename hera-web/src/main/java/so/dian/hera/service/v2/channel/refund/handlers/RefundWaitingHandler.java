package so.dian.hera.service.v2.channel.refund.handlers;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.domain.RefundDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.service.v2.channel.refund.RefundStateHandler;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.ValidateUtil;

@Slf4j
@Component
@Order(1000)
public class RefundWaitingHandler implements RefundStateHandler {

    @Resource
    private RefundMapper refundMapper;

    @Override
    public ChannelRetMsg.ChannelState getSupportedState() {
        return ChannelRetMsg.ChannelState.WAITING;
    }

    /**
     * 退款处理中状态处理逻辑，如果渠道有订单号，则更新退款单的渠道订单号。
     * 注意这里不是终态，后续有保障消息去重试。
     */
    @Override
    public void handle(RefundDO refundDO, UnifiedRefundRS rs) {
        String refundChannelNo = rs.getRefundChannelNo();
        log.info("RefundWaitingHandler，refundNo: {}, refundChannelNo: {}", refundDO.getRefundNo(), rs.getRefundChannelNo());
        if (StringUtils.isNotEmpty(refundChannelNo) && StringUtils.isEmpty(refundDO.getOutTraceNo())) {
            refundDO.setOutTraceNo(refundChannelNo);
            int updateResult = refundMapper.update(refundDO);
            ValidateUtil.requiredTrue(updateResult == 1, HeraBizErrorCodeEnum.ILLEGAL_ERROR);
        }
        log.info("RefundWaitingHandler，refundNo: {}, rs: {}", refundDO.getRefundNo(), JSON.toJSONString(rs));
    }
}
