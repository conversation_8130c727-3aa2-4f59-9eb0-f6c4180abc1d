package so.dian.hera.controller.v2.auth;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.apiv2.UnifiedPayAuthApi;
import com.chargebolt.hera.client.apiv2.UnifiedPayAuthQueryApi;
import com.chargebolt.hera.client.dto.param.rq.*;
import com.chargebolt.hera.client.dto.param.rs.RentalAuthRecordRS;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthOrderQueryRS;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayAuthRS;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayCloseRS;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;
import so.dian.hera.service.v2.UnifiedPayAuthQueryService;
import so.dian.hera.service.v2.UnifiedPayAuthService;
import so.dian.mofa3.lang.domain.PageContext;
import so.dian.mofa3.lang.domain.Result;
import javax.annotation.Resource;

/**
 * 预授权接口
 */
@Slf4j
@RestController
@Api("统一支付-创建支付预授权")
public class UnifiedPayAuthController implements UnifiedPayAuthApi, UnifiedPayAuthQueryApi {

    @Resource
    private UnifiedPayAuthService unifiedPayAuthService;

    @Resource
    private UnifiedPayAuthQueryService unifiedPayAuthQueryService;

    /**
     * 创建支付预授权
     * @param rq
     * @return
     */
    @Override
    @ApiOperation(value = "创建支付预授权")
    public Result<UnifiedPayAuthRS> unifiedPayAuth(UnifiedPayAuthRQ rq) {
        log.info("统一支付创建预授权#请求 rq: {}", JSON.toJSONString(rq));
        Result<UnifiedPayAuthRS> rs = unifiedPayAuthService.unifiedPayAuth(rq);
        log.info("统一支付创建预授权#响应 rs: {}", JSON.toJSONString(rs));
        return rs;
    }

    /**
     * 取消预授权
     * @param rq
     * @return
     */
    @ApiOperation(value = "取消预授权")
    @Override
    public Result<Boolean> cancelAuth(UnifiedPayAuthCancelRQ rq) {
        log.info("取消预授权#请求 rq: {}", JSON.toJSONString(rq));
        boolean revokeAuth = unifiedPayAuthService.cancelAuth(rq);
        log.info("取消预授权#响应 revokeAuth: {}", revokeAuth);
        return Result.success(revokeAuth);
    }


    @Override
    public Result<Boolean> updateRentalAuthOrder(UpdateRentalAuthOrderRQ rq) {
        log.info("更新租借授权订单#请求 rq: {}", JSON.toJSONString(rq));
        Result<Boolean> rs = unifiedPayAuthService.updateRentalAuthOrder(rq);
        log.info("更新租借授权订单#响应 rs: {}", JSON.toJSONString(rs));
        return rs;
    }



    /**
     * 根据用户信息查询符合条件的授权成功的授权凭证
     * @param rq
     * @return
     */
    @Override
    public Result<UnifiedPayAuthOrderQueryRS> unifiedPayAuthQryByParams(UnifiedPayAuthOrderQueryRQ rq) {
        log.info("根据用户信息查询支付预授权#请求 rq: {}", JSON.toJSONString(rq));
        Result<UnifiedPayAuthOrderQueryRS> rs = unifiedPayAuthQueryService.unifiedPayAuthQryByParams(rq);
        log.info("根据用户信息查询支付预授权#响应 rs: {}", JSON.toJSONString(rs));
        return rs;
    }

    @Override
    public Result<UnifiedPayAuthOrderQueryRS> unifiedPayAuthQryByPayAuthNo(String payAuthNo) {
        log.info("根据授权单号查询支付预授权#请求 rq: {}", payAuthNo);
        Result<UnifiedPayAuthOrderQueryRS> rs = unifiedPayAuthQueryService.unifiedPayAuthQryByPayAuthNo(payAuthNo);
        log.info("根据授权单号查询支付预授权#响应 rs: {}", JSON.toJSONString(rs));
        return rs;
    }

    @Override
    public Result<UnifiedPayAuthOrderQueryRS> unifiedPayAuthQryByOrderNo(String orderNo) {
        log.info("根据订单号查询支付预授权#请求 rq: {}", orderNo);
        Result<UnifiedPayAuthOrderQueryRS> rs = unifiedPayAuthQueryService.unifiedPayAuthQryByOrderNo(orderNo);
        log.info("根据订单号查询支付预授权#响应 rs: {}", JSON.toJSONString(rs));
        return rs;
    }

    @Override
    public Result<UnifiedPayAuthOrderQueryRS> unifiedPayAuthQryByChannelAuthNo(String channelAuthNo) {
        log.info("根据渠道授权单号查询支付预授权#请求 rq: {}", channelAuthNo);
        Result<UnifiedPayAuthOrderQueryRS> rs = unifiedPayAuthQueryService.unifiedPayAuthQryByChannelAuthNo(channelAuthNo);
        log.info("根据渠道授权单号查询支付预授权#响应 rs: {}", JSON.toJSONString(rs));
        return rs;
    }

    @Override
    public Result<PageContext<RentalAuthRecordRS>> qryAuthPage( UnifiedPayAuthQueryRQ request) {
        return unifiedPayAuthService.queryRentalAuthList(request);
    }

    @Override
    public Result<UnifiedPayAuthOrderQueryRS> getLastRentalAuthRecord(Long tenantId, Long userId) {
        log.info("获取用户最新一条预授权记录#请求 tenantId: {},userId: {}", tenantId, userId);
        Result<UnifiedPayAuthOrderQueryRS> rs = unifiedPayAuthQueryService.getLastRentalAuthRecord(tenantId, userId);
        log.info("获取用户最新一条预授权记录#响应 rs: {}", JSON.toJSONString(rs));
        return rs;
    }



}
