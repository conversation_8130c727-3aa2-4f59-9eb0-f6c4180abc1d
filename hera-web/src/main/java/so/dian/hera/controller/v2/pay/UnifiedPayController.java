package so.dian.hera.controller.v2.pay;

import javax.annotation.Resource;
import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.apiv2.UnifiedPayApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;
import com.chargebolt.hera.client.dto.param.rq.UnifiedPayRQ;
import com.chargebolt.hera.client.dto.param.rs.UnifiedPayRS;
import so.dian.hera.service.v2.UnifiedPayService;
import so.dian.mofa3.lang.domain.Result;

/**
 * 统一支付下单接口
 */
@Slf4j
@RestController
@Api("统一支付-创建支付单")
public class UnifiedPayController implements UnifiedPayApi {

    @Resource
    private UnifiedPayService unifiedPayService;

    /**
     * 统一支付下单
     * @param rq
     * @return
     */
    @Override
    @ApiOperation(value = "统一支付下单")
    public Result<UnifiedPayRS> unifiedPay(@RequestBody UnifiedPayRQ rq){
        log.info("统一支付#请求 rq: {}", JSON.toJSONString(rq));
        Result<UnifiedPayRS> result = unifiedPayService.unifiedPay(rq);
        log.info("统一支付#响应 rs: {}", JSON.toJSONString(result));
        return result;
    }

}
