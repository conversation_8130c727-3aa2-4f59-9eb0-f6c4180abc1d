# 退款服务配置示例
# 在 application.yml 或 application.properties 中添加以下配置

# ===== 简化版退款服务配置 =====
hera:
  refund:
    # 是否启用简化版退款服务 (true: 启用新架构, false: 使用原有架构)
    enable-simplified-service: false

    # 灰度比例 (0-100)
    # 0: 不使用新架构
    # 100: 全量使用新架构
    # 1-99: 按比例灰度切换
    simplified-service-gray-ratio: 0

    # 是否启用详细日志
    enable-detailed-logging: true

    # 是否启用性能监控
    enable-performance-monitoring: true

    # 每个步骤的超时时间(毫秒)
    step-timeout-ms: 30000

    # 是否在新架构失败时自动回退到旧架构
    enable-fallback-to-old-service: true

    # 连续失败多少次后触发回退
    fallback-failure-threshold: 3

    # 回退后的恢复检查间隔(分钟)
    fallback-recovery-check-interval-minutes: 5

# ===== 使用说明 =====
#
# 1. 开发环境测试
#    enable-simplified-service: true
#    simplified-service-gray-ratio: 100
#    enable-detailed-logging: true
#    enable-performance-monitoring: true
#
# 2. 生产环境灰度
#    enable-simplified-service: true
#    simplified-service-gray-ratio: 5  # 先5%灰度
#    enable-fallback-to-old-service: true
#
# 3. 生产环境全量
#    enable-simplified-service: true
#    simplified-service-gray-ratio: 100
#
# 4. 紧急回退
#    enable-simplified-service: false
#
# ===== 监控指标说明 =====
#
# 日志中会输出以下关键指标：
# - 退款监控 - 简化版服务开始执行
# - 退款监控 - 简化版服务执行成功/失败
# - 退款监控 - 原有服务执行成功/失败
# - 性能指标 - 各步骤执行时间
# - 成功率统计
# - 回退统计
#
# 可以通过日志分析工具或监控系统收集这些指标进行分析