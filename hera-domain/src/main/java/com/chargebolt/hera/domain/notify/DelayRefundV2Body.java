/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.hera.domain.notify;

import java.io.Serializable;

import lombok.Data;
import so.dian.commons.eden.constant.MqDelayLevelConst;

/**
 * v2 延迟退款消息体，字段命名与 RefundRQ 尽量一致
 */
@Data
public class DelayRefundV2Body implements Serializable {
    private static final long serialVersionUID = 171202406179163249L;

    /** 业务交易号，对应 RefundRQ.bizTradeNo */
    private String bizTradeNo;

    /** 退款原因 */
    private String refundReason;

    /** 退款金额 */
    private Long amount;

    /** 发起系统 */
    private String system;

    /** 退款用户ID（可选） */
    private Long userId;

    /** MQ 延迟级别，默认1分钟 */
    private Integer delayLevel = MqDelayLevelConst.DELAY_1M;
}


