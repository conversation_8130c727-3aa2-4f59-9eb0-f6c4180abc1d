package com.chargebolt.hera.domain;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PaymentOrderDO {
    /** 主键ID */
    private Long id;
    /** 用户ID */
    private Long userId;
    /** 业务交易流水号 */
    private String bizTradeNo;
    /** 支付流水号 */
    private String payTradeNo;
    /** 支付渠道流水号 */
    private String payChannelNo;
    /** 支付授权单号 */
    private String payAuthNo;
    /** 支付业务类型：11-支付订单,12-支付押金,23-信用卡预授权 {@link com.chargebolt.hera.client.enums.PaymentBizTypeEnum} */
    private Integer bizType;
    /** 支付类型：5-微信小程序,6-钱包,7-押金抵扣 {@link com.chargebolt.hera.client.enums.PaywayEnum} */
    private Integer payType;
    /** 支付方式：0-未知,201-支付宝,203-微信 {@link com.chargebolt.hera.client.enums.PayMethodEnum} */
    private Integer payMethod;
    /** 客户端类型：1-普通h5,2-微信小程序,4-安卓客户端,5-iOS客户端 {@link com.chargebolt.eden.enums.ClientTypeEnum} */
    private Integer clientType;
    /** 代理商ID */
    private Long tenantId;
    /** 支付渠道配置记录主键ID */
    private Long payConfigId;
    /** 状态：1-初始化,2-已支付,6-退款中,7-已退款,8-退款失败,9-支付取消,99-失败 {@link com.chargebolt.hera.client.enums.status.PayStatus} */
    private Integer status;
    /** 支付金额，实际支付（转汇后的金额） */
    private Long payAmount;
    /** 退款金额，实际退款（转汇后的金额） */
    private Long refundAmount;
    /** 渠道优惠金额 */
    private Long channelDiscountAmount;
    /** 币种: 默认人民币 */
    private String currency;
    /** 支付成功时间 */
    private Date payTime;
    /** 退款成功时间 */
    private Date refundTime;
    /** 扩展字段，json格式 {@link com.chargebolt.hera.domain.PaymentJsonExtensions}*/
    private String jsonExtensions;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    /** 创建时间戳 */
    private Long gmtCreate = System.currentTimeMillis();
    /** 更新时间戳 */
    private Long gmtUpdate = System.currentTimeMillis();

}
