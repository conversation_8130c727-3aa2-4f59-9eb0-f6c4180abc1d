package so.dian.hera.interceptor.v2;

import so.dian.hera.interceptor.utils.TradeNoGeneratorUtil;
import so.dian.hera.interceptor.v2.model.PayAuthChannelContext;

public abstract class AbstractPayAuthService {

    public String customPayAuthId(PayAuthChannelContext context) {
        return TradeNoGeneratorUtil.generateAuthNo(context.getAuthRq().getBizType(), context.getAuthRq().getCustomerInfo().getUserId());
    }

}
