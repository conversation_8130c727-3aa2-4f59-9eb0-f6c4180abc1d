package so.dian.hera.interceptor.v2.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayChannelCloseContext {

    /** 支付渠道编号 */
    private String payTradeNo;

    /** 三方渠道编号 */
    private String payChannelNo;

    /** 支付配置ID */
    private Long payConfigId;

}
